"""
CLEAR Responsive Design Testing Framework

A comprehensive responsive design testing framework for the CLEAR platform,
built on Playwright and Django. This framework tests actual responsive behavior,
layout changes, and component adaptations across different device sizes.

Created: 2025-07-28
Task: #46 - Add responsive design testing across all device sizes
Framework: Playwright + Django + Bootstrap 5.3.0
"""

__version__ = "1.0.0"
__author__ = "CLEAR Development Team"
__email__ = "<EMAIL>"
__description__ = "Comprehensive responsive design testing framework"

# Import main classes for easy access
try:
    from .test_responsive_design import (
        ResponsiveDesignConfig,
        ResponsiveDesignTester,
        ResponsiveTestResult,
        ResponsiveDesignTestSuite
    )

    __all__ = [
        'ResponsiveDesignConfig',
        'ResponsiveDesignTester',
        'ResponsiveTestResult',
        'ResponsiveDesignTestSuite'
    ]

except ImportError:
    # Handle case where <PERSON><PERSON> is not installed
    __all__ = []

    def _missing_playwright_error():
        raise ImportError(
            "Playwright is required for responsive design testing. "
            "Install it with: pip install playwright && npx playwright install"
        )

    # Create placeholder classes that raise helpful errors
    class ResponsiveDesignConfig:
        def __init__(self, *args, **kwargs):
            _missing_playwright_error()

    class ResponsiveDesignTester:
        def __init__(self, *args, **kwargs):
            _missing_playwright_error()

    class ResponsiveTestResult:
        def __init__(self, *args, **kwargs):
            _missing_playwright_error()

    class ResponsiveDesignTestSuite:
        def __init__(self, *args, **kwargs):
            _missing_playwright_error()
