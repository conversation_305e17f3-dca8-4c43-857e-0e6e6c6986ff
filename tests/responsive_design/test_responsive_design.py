"""
Comprehensive Responsive Design Testing Framework

This module provides comprehensive responsive design testing across all device sizes,
testing actual responsive behavior, layout changes, and component adaptations.
Built upon the existing Playwright infrastructure.

Created: 2025-07-28
Task: #46 - Add responsive design testing across all device sizes
"""

import os
import time
import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, field
import pytest
import logging

from django.test import TestCase, TransactionTestCase, LiveServerTestCase
from django.contrib.auth import get_user_model
from django.contrib.staticfiles.testing import StaticLiveServerTestCase
from django.test.utils import override_settings

from apps.projects.models import Project
from apps.infrastructure.models import UtilityLineData
from apps.documents.models import Document
from apps.authentication.models import Organization

User = get_user_model()
logger = logging.getLogger(__name__)

# Playwright imports
try:
    from playwright.sync_api import sync_playwright, <PERSON>, <PERSON>rowser, BrowserContext
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    logger.warning("Playwright not available - responsive design tests will be skipped")


@dataclass
class ResponsiveTestResult:
    """Result of a responsive design test."""
    test_name: str
    viewport_name: str
    viewport_size: Tuple[int, int]
    component_name: str
    layout_valid: bool
    breakpoint_behavior: Dict[str, Any]
    accessibility_score: float
    performance_metrics: Dict[str, Any]
    issues_found: List[str] = field(default_factory=list)
    passed: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


class ResponsiveDesignConfig:
    """Configuration for responsive design testing."""

    # Base directories
    BASE_DIR = Path(__file__).parent
    REPORTS_DIR = BASE_DIR / "reports"
    SCREENSHOTS_DIR = BASE_DIR / "screenshots"

    # Comprehensive viewport sizes for responsive testing
    VIEWPORT_SIZES = {
        # Mobile devices
        'mobile_small': {'width': 320, 'height': 568, 'device': 'iPhone SE'},
        'mobile_medium': {'width': 375, 'height': 667, 'device': 'iPhone 6/7/8'},
        'mobile_large': {'width': 414, 'height': 896, 'device': 'iPhone XR'},

        # Tablets
        'tablet_portrait': {'width': 768, 'height': 1024, 'device': 'iPad Portrait'},
        'tablet_landscape': {'width': 1024, 'height': 768, 'device': 'iPad Landscape'},
        'tablet_large': {'width': 834, 'height': 1194, 'device': 'iPad Pro 11"'},

        # Desktop
        'desktop_small': {'width': 1366, 'height': 768, 'device': 'Laptop'},
        'desktop_medium': {'width': 1920, 'height': 1080, 'device': 'Desktop FHD'},
        'desktop_large': {'width': 2560, 'height': 1440, 'device': 'Desktop 2K'},
        'desktop_ultra': {'width': 3840, 'height': 2160, 'device': 'Desktop 4K'},
    }

    # Bootstrap 5 breakpoints (matching project's Bootstrap 5.3.0)
    BOOTSTRAP_BREAKPOINTS = {
        'xs': 0,      # Extra small devices
        'sm': 576,    # Small devices
        'md': 768,    # Medium devices
        'lg': 992,    # Large devices
        'xl': 1200,   # Extra large devices
        'xxl': 1400,  # Extra extra large devices
    }

    # Component selectors for responsive testing
    RESPONSIVE_COMPONENTS = {
        'navigation': {
            'selector': '.navbar, .nav, .navigation',
            'mobile_behavior': 'collapse',
            'desktop_behavior': 'horizontal'
        },
        'sidebar': {
            'selector': '.sidebar, .side-nav, .drawer',
            'mobile_behavior': 'hidden',
            'desktop_behavior': 'visible'
        },
        'grid_layout': {
            'selector': '.container, .row, .col',
            'mobile_behavior': 'stack',
            'desktop_behavior': 'grid'
        },
        'cards': {
            'selector': '.card, .panel, .widget',
            'mobile_behavior': 'full_width',
            'desktop_behavior': 'grid'
        },
        'tables': {
            'selector': 'table, .table, .data-table',
            'mobile_behavior': 'scroll',
            'desktop_behavior': 'full'
        },
        'forms': {
            'selector': 'form, .form, .form-group',
            'mobile_behavior': 'stack',
            'desktop_behavior': 'inline'
        }
    }

    # Performance thresholds
    PERFORMANCE_THRESHOLDS = {
        'load_time': 3000,      # 3 seconds
        'first_paint': 1000,    # 1 second
        'largest_contentful_paint': 2500,  # 2.5 seconds
        'cumulative_layout_shift': 0.1,    # CLS score
    }

    @classmethod
    def ensure_directories(cls):
        """Ensure all required directories exist."""
        for directory in [cls.REPORTS_DIR, cls.SCREENSHOTS_DIR]:
            directory.mkdir(parents=True, exist_ok=True)


class ResponsiveDesignTester:
    """Core responsive design testing functionality using Playwright."""

    def __init__(self, page: Page, config: ResponsiveDesignConfig):
        self.page = page
        self.config = config
        self.config.ensure_directories()
        self.test_results = []

    def test_viewport_behavior(
        self,
        url_path: str,
        component_name: str,
        viewport_name: str
    ) -> ResponsiveTestResult:
        """Test responsive behavior at specific viewport."""
        viewport_config = self.config.VIEWPORT_SIZES[viewport_name]
        viewport_size = (viewport_config['width'], viewport_config['height'])

        # Set viewport
        self.page.set_viewport_size({
            'width': viewport_config['width'],
            'height': viewport_config['height']
        })

        # Navigate to page
        self.page.goto(url_path)
        self.page.wait_for_load_state('networkidle')

        # Test component behavior
        component_config = self.config.RESPONSIVE_COMPONENTS.get(component_name, {})
        selector = component_config.get('selector', f'.{component_name}')

        result = ResponsiveTestResult(
            test_name=f"{component_name}_{viewport_name}",
            viewport_name=viewport_name,
            viewport_size=viewport_size,
            component_name=component_name,
            layout_valid=True,
            breakpoint_behavior={},
            accessibility_score=0.0,
            performance_metrics={}
        )

        # Test layout validity
        result.layout_valid = self._test_layout_validity(selector)

        # Test breakpoint behavior
        result.breakpoint_behavior = self._test_breakpoint_behavior(
            selector, viewport_config['width']
        )

        # Test accessibility
        result.accessibility_score = self._test_accessibility(selector)

        # Test performance
        result.performance_metrics = self._test_performance()

        # Determine if test passed
        result.passed = (
            result.layout_valid and
            result.accessibility_score >= 0.8 and
            len(result.issues_found) == 0
        )

        return result

    def _test_layout_validity(self, selector: str) -> bool:
        """Test if layout is valid (no overflow, proper spacing)."""
        try:
            elements = self.page.locator(selector).all()
            if not elements:
                return False

            for element in elements:
                # Check if element is visible
                if not element.is_visible():
                    continue

                # Get element bounding box
                box = element.bounding_box()
                if not box:
                    continue

                # Check for horizontal overflow
                viewport_width = self.page.viewport_size['width']
                if box['x'] + box['width'] > viewport_width:
                    return False

                # Check for negative positioning
                if box['x'] < 0 or box['y'] < 0:
                    return False

            return True
        except Exception as e:
            logger.error(f"Layout validity test failed: {e}")
            return False

    def _test_breakpoint_behavior(self, selector: str, viewport_width: int) -> Dict[str, Any]:
        """Test breakpoint-specific behavior."""
        behavior = {}

        try:
            # Determine current breakpoint
            current_breakpoint = 'xs'
            for bp_name, bp_width in self.config.BOOTSTRAP_BREAKPOINTS.items():
                if viewport_width >= bp_width:
                    current_breakpoint = bp_name

            behavior['current_breakpoint'] = current_breakpoint

            # Test navigation collapse behavior
            if 'nav' in selector.lower():
                toggle_button = self.page.locator('.navbar-toggler').first
                if viewport_width < 992:  # Bootstrap lg breakpoint
                    behavior['nav_collapsed'] = toggle_button.is_visible()
                else:
                    behavior['nav_expanded'] = not toggle_button.is_visible()

            # Test grid behavior
            if 'col' in selector or 'grid' in selector:
                cols = self.page.locator('[class*="col-"]').all()
                behavior['column_count'] = len(cols)

                # Check if columns stack on mobile
                if viewport_width < 768 and len(cols) > 1:  # Bootstrap md breakpoint
                    first_col = cols[0] if cols else None
                    second_col = cols[1] if len(cols) > 1 else None

                    if first_col and second_col:
                        first_box = first_col.bounding_box()
                        second_box = second_col.bounding_box()

                        if first_box and second_box:
                            behavior['columns_stacked'] = second_box['y'] > first_box['y'] + first_box['height']

        except Exception as e:
            logger.error(f"Breakpoint behavior test failed: {e}")
            behavior['error'] = str(e)

        return behavior

    def _test_accessibility(self, selector: str) -> float:
        """Test accessibility features."""
        score = 1.0

        try:
            # Check for proper ARIA labels
            elements = self.page.locator(selector).all()
            for element in elements:
                if not element.is_visible():
                    continue

                # Check for interactive elements without labels
                tag_name = element.evaluate('el => el.tagName.toLowerCase()')
                if tag_name in ['button', 'a', 'input']:
                    aria_label = element.get_attribute('aria-label')
                    title = element.get_attribute('title')
                    text_content = element.text_content()

                    if not any([aria_label, title, text_content]):
                        score -= 0.2

            # Check color contrast (simplified)
            # In a real implementation, you'd use axe-core or similar

        except Exception as e:
            logger.error(f"Accessibility test failed: {e}")
            score = 0.5

        return max(0.0, score)

    def _test_performance(self) -> Dict[str, Any]:
        """Test performance metrics."""
        metrics = {}

        try:
            # Get performance timing
            timing = self.page.evaluate("""
                () => {
                    const timing = performance.timing;
                    const navigation = performance.getEntriesByType('navigation')[0];
                    return {
                        loadTime: timing.loadEventEnd - timing.navigationStart,
                        domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
                        firstPaint: navigation ? navigation.loadEventEnd : 0
                    };
                }
            """)

            metrics.update(timing)

            # Check against thresholds
            metrics['load_time_ok'] = timing.get('loadTime', 0) < self.config.PERFORMANCE_THRESHOLDS['load_time']

        except Exception as e:
            logger.error(f"Performance test failed: {e}")
            metrics['error'] = str(e)

        return metrics


@pytest.mark.skipif(not PLAYWRIGHT_AVAILABLE, reason="Playwright not available")
class ResponsiveDesignTestSuite(StaticLiveServerTestCase):
    """Comprehensive responsive design test suite."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.config = ResponsiveDesignConfig()
        cls.config.ensure_directories()

        # Initialize Playwright
        cls.playwright = sync_playwright().start()
        cls.browser = cls.playwright.chromium.launch(headless=True)
        cls.context = cls.browser.new_context()
        cls.page = cls.context.new_page()

        cls.tester = ResponsiveDesignTester(cls.page, cls.config)
        cls.test_results = []

    @classmethod
    def tearDownClass(cls):
        # Generate comprehensive report
        cls._generate_test_report()

        # Cleanup Playwright
        cls.context.close()
        cls.browser.close()
        cls.playwright.stop()

        super().tearDownClass()

    def setUp(self):
        """Set up test data."""
        # Create test organization
        self.organization = Organization.objects.create(
            name="Responsive Test Org",
            slug="responsive-test-org"
        )

        # Create test user
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            organization=self.organization
        )

        # Create test project
        self.project = Project.objects.create(
            name="Responsive Test Project",
            description="Project for responsive design testing",
            organization=self.organization,
            created_by=self.user
        )

    def _run_responsive_test(self, url_path: str, component_name: str, viewports: List[str] = None):
        """Run responsive design test across multiple viewports."""
        if viewports is None:
            viewports = list(self.config.VIEWPORT_SIZES.keys())

        for viewport_name in viewports:
            result = self.tester.test_viewport_behavior(
                f"{self.live_server_url}{url_path}",
                component_name,
                viewport_name
            )

            self.test_results.append(result)

            # Assert test passed
            if not result.passed:
                issues = ', '.join(result.issues_found) if result.issues_found else 'Layout or accessibility issues'
                self.fail(
                    f"Responsive design test failed for {component_name} at {viewport_name}: {issues}"
                )

    def test_navigation_responsive_behavior(self):
        """Test navigation component responsive behavior."""
        self._run_responsive_test('/dashboard/', 'navigation')

    def test_sidebar_responsive_behavior(self):
        """Test sidebar component responsive behavior."""
        self._run_responsive_test('/dashboard/', 'sidebar')

    def test_grid_layout_responsive_behavior(self):
        """Test grid layout responsive behavior."""
        self._run_responsive_test('/projects/', 'grid_layout')

    def test_cards_responsive_behavior(self):
        """Test card components responsive behavior."""
        self._run_responsive_test('/projects/', 'cards')

    def test_tables_responsive_behavior(self):
        """Test table components responsive behavior."""
        self._run_responsive_test('/projects/', 'tables')

    def test_forms_responsive_behavior(self):
        """Test form components responsive behavior."""
        self._run_responsive_test('/projects/create/', 'forms')

    @classmethod
    def _generate_test_report(cls):
        """Generate comprehensive responsive design test report."""
        report_data = {
            'test_summary': {
                'total_tests': len(cls.test_results),
                'passed_tests': sum(1 for r in cls.test_results if r.passed),
                'failed_tests': sum(1 for r in cls.test_results if not r.passed),
                'test_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'framework': 'Playwright + Django',
                'viewports_tested': list(cls.config.VIEWPORT_SIZES.keys())
            },
            'viewport_analysis': {},
            'component_analysis': {},
            'performance_summary': {},
            'accessibility_summary': {},
            'detailed_results': []
        }

        # Analyze results by viewport
        for viewport_name in cls.config.VIEWPORT_SIZES.keys():
            viewport_results = [r for r in cls.test_results if r.viewport_name == viewport_name]
            report_data['viewport_analysis'][viewport_name] = {
                'total_tests': len(viewport_results),
                'passed_tests': sum(1 for r in viewport_results if r.passed),
                'avg_accessibility_score': sum(r.accessibility_score for r in viewport_results) / len(viewport_results) if viewport_results else 0,
                'device_info': cls.config.VIEWPORT_SIZES[viewport_name]
            }

        # Analyze results by component
        components = set(r.component_name for r in cls.test_results)
        for component_name in components:
            component_results = [r for r in cls.test_results if r.component_name == component_name]
            report_data['component_analysis'][component_name] = {
                'total_tests': len(component_results),
                'passed_tests': sum(1 for r in component_results if r.passed),
                'avg_accessibility_score': sum(r.accessibility_score for r in component_results) / len(component_results) if component_results else 0,
                'problematic_viewports': [r.viewport_name for r in component_results if not r.passed]
            }

        # Add detailed results
        for result in cls.test_results:
            report_data['detailed_results'].append({
                'test_name': result.test_name,
                'viewport_name': result.viewport_name,
                'viewport_size': result.viewport_size,
                'component_name': result.component_name,
                'passed': result.passed,
                'layout_valid': result.layout_valid,
                'accessibility_score': result.accessibility_score,
                'breakpoint_behavior': result.breakpoint_behavior,
                'performance_metrics': result.performance_metrics,
                'issues_found': result.issues_found
            })

        # Save report
        report_path = cls.config.REPORTS_DIR / f"responsive_design_report_{int(time.time())}.json"
        with open(report_path, 'w') as f:
            json.dump(report_data, f, indent=2)

        logger.info(f"Responsive design test report saved to: {report_path}")

        # Generate HTML report
        html_report = cls._generate_html_report(report_data)
        html_path = cls.config.REPORTS_DIR / f"responsive_design_report_{int(time.time())}.html"
        with open(html_path, 'w') as f:
            f.write(html_report)

        logger.info(f"HTML report saved to: {html_path}")

    @classmethod
    def _generate_html_report(cls, report_data: Dict[str, Any]) -> str:
        """Generate HTML report."""
        html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>CLEAR Responsive Design Test Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #007bff; color: white; padding: 20px; border-radius: 5px; }}
                .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }}
                .card {{ background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }}
                .passed {{ border-left-color: #28a745; }}
                .failed {{ border-left-color: #dc3545; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f8f9fa; }}
                .viewport-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>CLEAR Responsive Design Test Report</h1>
                <p>Generated: {report_data['test_summary']['test_date']}</p>
            </div>

            <div class="summary">
                <div class="card">
                    <h3>Total Tests</h3>
                    <p style="font-size: 2em; margin: 0;">{report_data['test_summary']['total_tests']}</p>
                </div>
                <div class="card passed">
                    <h3>Passed</h3>
                    <p style="font-size: 2em; margin: 0;">{report_data['test_summary']['passed_tests']}</p>
                </div>
                <div class="card failed">
                    <h3>Failed</h3>
                    <p style="font-size: 2em; margin: 0;">{report_data['test_summary']['failed_tests']}</p>
                </div>
            </div>

            <h2>Viewport Analysis</h2>
            <div class="viewport-grid">
        """

        for viewport_name, analysis in report_data['viewport_analysis'].items():
            device_info = analysis['device_info']
            html += f"""
                <div class="card">
                    <h4>{viewport_name.replace('_', ' ').title()}</h4>
                    <p><strong>Device:</strong> {device_info['device']}</p>
                    <p><strong>Size:</strong> {device_info['width']}x{device_info['height']}</p>
                    <p><strong>Tests:</strong> {analysis['passed_tests']}/{analysis['total_tests']} passed</p>
                    <p><strong>Avg Accessibility:</strong> {analysis['avg_accessibility_score']:.2f}</p>
                </div>
            """

        html += """
            </div>

            <h2>Component Analysis</h2>
            <table>
                <thead>
                    <tr>
                        <th>Component</th>
                        <th>Tests Passed</th>
                        <th>Avg Accessibility Score</th>
                        <th>Problematic Viewports</th>
                    </tr>
                </thead>
                <tbody>
        """

        for component_name, analysis in report_data['component_analysis'].items():
            problematic = ', '.join(analysis['problematic_viewports']) if analysis['problematic_viewports'] else 'None'
            html += f"""
                <tr>
                    <td>{component_name.replace('_', ' ').title()}</td>
                    <td>{analysis['passed_tests']}/{analysis['total_tests']}</td>
                    <td>{analysis['avg_accessibility_score']:.2f}</td>
                    <td>{problematic}</td>
                </tr>
            """

        html += """
                </tbody>
            </table>

            <h2>Detailed Results</h2>
            <table>
                <thead>
                    <tr>
                        <th>Test Name</th>
                        <th>Viewport</th>
                        <th>Component</th>
                        <th>Status</th>
                        <th>Layout Valid</th>
                        <th>Accessibility Score</th>
                    </tr>
                </thead>
                <tbody>
        """

        for result in report_data['detailed_results']:
            status_class = 'passed' if result['passed'] else 'failed'
            status_text = 'PASS' if result['passed'] else 'FAIL'
            html += f"""
                <tr class="{status_class}">
                    <td>{result['test_name']}</td>
                    <td>{result['viewport_name']}</td>
                    <td>{result['component_name']}</td>
                    <td><strong>{status_text}</strong></td>
                    <td>{'✓' if result['layout_valid'] else '✗'}</td>
                    <td>{result['accessibility_score']:.2f}</td>
                </tr>
            """

        html += """
                </tbody>
            </table>
        </body>
        </html>
        """

        return html
