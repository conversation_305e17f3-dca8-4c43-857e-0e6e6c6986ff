#!/usr/bin/env python
"""
Log Management Script for CLEAR Application

This script handles log rotation, retention, and cleanup based on
configured policies. It can be run as a cron job for automated
log management.
"""

import argparse
import gzip
import os
import shutil
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.production')
import django
django.setup()

from django.conf import settings
from config.logging.logging_config import LOG_RETENTION_POLICIES


class LogManager:
    """
    Manages log files according to retention policies.
    """

    def __init__(self, log_dir=None, dry_run=False, verbose=False):
        self.log_dir = Path(log_dir) if log_dir else Path(settings.BASE_DIR) / 'logs'
        self.dry_run = dry_run
        self.verbose = verbose
        self.policies = LOG_RETENTION_POLICIES

    def rotate_logs(self):
        """Rotate log files based on size and age."""
        print(f"Starting log rotation in {self.log_dir}")
        
        for log_file in self.log_dir.glob('*.log'):
            self._rotate_single_log(log_file)

    def _rotate_single_log(self, log_file):
        """Rotate a single log file."""
        if not log_file.exists():
            return

        # Get file stats
        file_size = log_file.stat().st_size
        file_age = datetime.now() - datetime.fromtimestamp(log_file.stat().st_mtime)

        # Determine policy based on log file name
        policy_name = self._get_policy_name(log_file.name)
        policy = self.policies.get(policy_name, self.policies['general'])

        if self.verbose:
            print(f"Checking {log_file.name}: {file_size / 1024 / 1024:.1f}MB, {file_age.days} days old")

        # Check if rotation is needed
        max_size_bytes = policy['max_size_mb'] * 1024 * 1024
        max_age_days = policy['days']

        if file_size > max_size_bytes or file_age.days > max_age_days:
            self._perform_rotation(log_file, policy)

    def _get_policy_name(self, filename):
        """Determine policy name from filename."""
        if 'error' in filename:
            return 'error'
        elif 'security' in filename:
            return 'security'
        elif 'business' in filename:
            return 'business'
        elif 'audit' in filename:
            return 'audit'
        elif 'performance' in filename:
            return 'performance'
        else:
            return 'general'

    def _perform_rotation(self, log_file, policy):
        """Perform the actual log rotation."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        rotated_name = f"{log_file.stem}_{timestamp}.log"
        rotated_path = log_file.parent / rotated_name

        if self.verbose:
            print(f"Rotating {log_file.name} to {rotated_name}")

        if not self.dry_run:
            # Copy the log file with timestamp
            shutil.copy2(log_file, rotated_path)
            
            # Compress the rotated file
            compressed_path = rotated_path.with_suffix('.log.gz')
            with open(rotated_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # Remove uncompressed rotated file
            rotated_path.unlink()
            
            # Truncate original log file
            with open(log_file, 'w') as f:
                f.write('')

            print(f"Rotated and compressed {log_file.name}")

    def cleanup_old_logs(self):
        """Remove old log files based on retention policies."""
        print(f"Starting log cleanup in {self.log_dir}")
        
        for log_file in self.log_dir.glob('*.log.gz'):
            self._cleanup_single_log(log_file)

    def _cleanup_single_log(self, log_file):
        """Clean up a single compressed log file."""
        # Extract timestamp from filename
        try:
            # Expected format: logname_YYYYMMDD_HHMMSS.log.gz
            parts = log_file.stem.split('_')
            if len(parts) >= 3:
                date_str = parts[-2]
                time_str = parts[-1].split('.')[0]  # Remove .log part
                timestamp_str = f"{date_str}_{time_str}"
                file_date = datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
            else:
                # Fallback to file modification time
                file_date = datetime.fromtimestamp(log_file.stat().st_mtime)
        except (ValueError, IndexError):
            # Fallback to file modification time
            file_date = datetime.fromtimestamp(log_file.stat().st_mtime)

        # Determine policy
        policy_name = self._get_policy_name(log_file.name)
        policy = self.policies.get(policy_name, self.policies['general'])

        # Check if file should be deleted
        age = datetime.now() - file_date
        if age.days > policy['days']:
            if self.verbose:
                print(f"Removing old log file: {log_file.name} ({age.days} days old)")
            
            if not self.dry_run:
                log_file.unlink()
                print(f"Removed {log_file.name}")

    def get_log_statistics(self):
        """Get statistics about log files."""
        stats = {
            'total_files': 0,
            'total_size_mb': 0,
            'by_type': {},
        }

        for log_file in self.log_dir.glob('*.log*'):
            if log_file.is_file():
                size_mb = log_file.stat().st_size / 1024 / 1024
                policy_name = self._get_policy_name(log_file.name)
                
                stats['total_files'] += 1
                stats['total_size_mb'] += size_mb
                
                if policy_name not in stats['by_type']:
                    stats['by_type'][policy_name] = {
                        'files': 0,
                        'size_mb': 0,
                    }
                
                stats['by_type'][policy_name]['files'] += 1
                stats['by_type'][policy_name]['size_mb'] += size_mb

        return stats

    def print_statistics(self):
        """Print log file statistics."""
        stats = self.get_log_statistics()
        
        print(f"\nLog Statistics for {self.log_dir}")
        print("=" * 50)
        print(f"Total files: {stats['total_files']}")
        print(f"Total size: {stats['total_size_mb']:.1f} MB")
        print()
        
        for log_type, type_stats in stats['by_type'].items():
            print(f"{log_type.title()} logs:")
            print(f"  Files: {type_stats['files']}")
            print(f"  Size: {type_stats['size_mb']:.1f} MB")
            print()

    def archive_logs(self, archive_dir=None, days_old=30):
        """Archive old logs to a separate directory."""
        if not archive_dir:
            archive_dir = self.log_dir / 'archive'
        else:
            archive_dir = Path(archive_dir)
        
        archive_dir.mkdir(exist_ok=True)
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        print(f"Archiving logs older than {days_old} days to {archive_dir}")
        
        for log_file in self.log_dir.glob('*.log.gz'):
            file_date = datetime.fromtimestamp(log_file.stat().st_mtime)
            
            if file_date < cutoff_date:
                archive_path = archive_dir / log_file.name
                
                if self.verbose:
                    print(f"Archiving {log_file.name}")
                
                if not self.dry_run:
                    shutil.move(str(log_file), str(archive_path))
                    print(f"Archived {log_file.name}")


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(description='Manage CLEAR application logs')
    parser.add_argument('--log-dir', help='Log directory path')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without actually doing it')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Rotate command
    rotate_parser = subparsers.add_parser('rotate', help='Rotate log files')
    
    # Cleanup command
    cleanup_parser = subparsers.add_parser('cleanup', help='Clean up old log files')
    
    # Stats command
    stats_parser = subparsers.add_parser('stats', help='Show log statistics')
    
    # Archive command
    archive_parser = subparsers.add_parser('archive', help='Archive old logs')
    archive_parser.add_argument('--archive-dir', help='Archive directory path')
    archive_parser.add_argument('--days-old', type=int, default=30, help='Archive logs older than N days')
    
    # All command
    all_parser = subparsers.add_parser('all', help='Run rotation, cleanup, and show stats')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Create log manager
    log_manager = LogManager(
        log_dir=args.log_dir,
        dry_run=args.dry_run,
        verbose=args.verbose
    )
    
    # Execute command
    if args.command == 'rotate':
        log_manager.rotate_logs()
    elif args.command == 'cleanup':
        log_manager.cleanup_old_logs()
    elif args.command == 'stats':
        log_manager.print_statistics()
    elif args.command == 'archive':
        log_manager.archive_logs(args.archive_dir, args.days_old)
    elif args.command == 'all':
        log_manager.rotate_logs()
        log_manager.cleanup_old_logs()
        log_manager.print_statistics()


if __name__ == '__main__':
    main()
