<!-- HTMX Error Fragment Template -->
<div class="alert alert-{{ error.severity|default:'danger' }} alert-dismissible fade show htmx-error" role="alert">
    <div class="d-flex align-items-start">
        <div class="flex-shrink-0 me-3">
            {% if error.severity == 'critical' %}
                <i class="fas fa-exclamation-circle fa-2x text-danger"></i>
            {% elif error.severity == 'high' %}
                <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
            {% elif error.severity == 'medium' %}
                <i class="fas fa-info-circle fa-2x text-info"></i>
            {% else %}
                <i class="fas fa-check-circle fa-2x text-success"></i>
            {% endif %}
        </div>
        
        <div class="flex-grow-1">
            <h6 class="alert-heading mb-2">
                {% if status_code == 404 %}
                    Resource Not Found
                {% elif status_code == 403 %}
                    Access Denied
                {% elif status_code == 401 %}
                    Authentication Required
                {% elif status_code == 422 %}
                    Validation Error
                {% else %}
                    Error Occurred
                {% endif %}
                
                {% if error_code %}
                <small class="text-muted">({{ error_code }})</small>
                {% endif %}
            </h6>
            
            <p class="mb-2">
                {{ message|default:"An error occurred while processing your request." }}
            </p>
            
            {% if error_id %}
            <div class="small text-muted mb-2">
                <strong>Error ID:</strong> {{ error_id }}
            </div>
            {% endif %}
            
            <!-- Action buttons for different error types -->
            <div class="mt-3">
                {% if status_code == 401 %}
                    <button type="button" class="btn btn-sm btn-primary" onclick="window.location.href='{% url 'authentication:login' %}'">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </button>
                {% elif status_code == 403 %}
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="history.back()">
                        <i class="fas fa-arrow-left"></i> Go Back
                    </button>
                {% elif status_code == 404 %}
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="htmx.ajax('GET', '{% url 'home' %}', {target: 'body'})">
                        <i class="fas fa-home"></i> Home
                    </button>
                {% elif status_code == 422 %}
                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="this.closest('.htmx-error').style.display='none'">
                        <i class="fas fa-edit"></i> Try Again
                    </button>
                {% else %}
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                        <i class="fas fa-redo"></i> Retry
                    </button>
                {% endif %}
                
                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="this.closest('.htmx-error').style.display='none'">
                    <i class="fas fa-times"></i> Dismiss
                </button>
            </div>
            
            {% if debug %}
            <div class="mt-3 pt-3 border-top">
                <details>
                    <summary class="text-muted small">Debug Information</summary>
                    <div class="mt-2 small">
                        {% if exception_type %}
                        <div><strong>Type:</strong> {{ exception_type }}</div>
                        {% endif %}
                        {% if exception_message %}
                        <div><strong>Message:</strong> {{ exception_message }}</div>
                        {% endif %}
                        {% if details %}
                        <div><strong>Details:</strong></div>
                        <pre class="small bg-light p-2 rounded">{{ details|pprint }}</pre>
                        {% endif %}
                    </div>
                </details>
            </div>
            {% endif %}
        </div>
        
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
</div>

<style>
.htmx-error {
    margin: 1rem 0;
    border-left: 4px solid;
}

.htmx-error.alert-danger {
    border-left-color: #dc3545;
}

.htmx-error.alert-warning {
    border-left-color: #ffc107;
}

.htmx-error.alert-info {
    border-left-color: #17a2b8;
}

.htmx-error.alert-success {
    border-left-color: #28a745;
}

.htmx-error details summary {
    cursor: pointer;
    user-select: none;
}

.htmx-error details summary:hover {
    text-decoration: underline;
}

.htmx-error pre {
    max-height: 200px;
    overflow-y: auto;
    font-size: 0.8rem;
}

/* Animation for HTMX error appearance */
.htmx-error {
    animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Auto-hide for low severity errors */
.htmx-error.alert-success {
    animation: slideInDown 0.3s ease-out, fadeOut 0.5s ease-out 4.5s forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}
</style>

<script>
// Auto-dismiss low severity errors after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const errorElement = document.querySelector('.htmx-error.alert-success');
    if (errorElement) {
        setTimeout(function() {
            errorElement.style.display = 'none';
        }, 5000);
    }
});

// Track error for analytics
if (typeof gtag !== 'undefined') {
    gtag('event', 'htmx_error', {
        'error_code': '{{ error_code|default:"unknown" }}',
        'status_code': {{ status_code|default:500 }},
        'severity': '{{ error.severity|default:"unknown" }}',
        'error_id': '{{ error_id|default:"" }}'
    });
}
</script>
