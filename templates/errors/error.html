{% extends "base.html" %}
{% load static %}

{% block title %}Error {{ status_code }} - CLEAR{% endblock %}

{% block extra_css %}
<style>
.error-container {
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.error-card {
    max-width: 600px;
    width: 100%;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.error-header {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    padding: 2rem;
    text-align: center;
}

.error-header.warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.error-header.info {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

.error-code {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.error-title {
    font-size: 1.5rem;
    margin-bottom: 0;
    opacity: 0.9;
}

.error-body {
    padding: 2rem;
}

.error-message {
    font-size: 1.1rem;
    color: #495057;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.error-id {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 0.75rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.error-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.error-actions .btn {
    flex: 1;
    min-width: 120px;
}

.debug-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #dee2e6;
}

.debug-details {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.debug-details pre {
    margin: 0;
    font-size: 0.85rem;
    white-space: pre-wrap;
    word-break: break-word;
}

.severity-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 1rem;
}

.severity-low { background: #d4edda; color: #155724; }
.severity-medium { background: #fff3cd; color: #856404; }
.severity-high { background: #f8d7da; color: #721c24; }
.severity-critical { background: #721c24; color: white; }

@media (max-width: 576px) {
    .error-container {
        padding: 1rem;
    }
    
    .error-header {
        padding: 1.5rem;
    }
    
    .error-code {
        font-size: 2.5rem;
    }
    
    .error-body {
        padding: 1.5rem;
    }
    
    .error-actions {
        flex-direction: column;
    }
    
    .error-actions .btn {
        flex: none;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <div class="error-card">
        <div class="error-header {% if error.severity == 'low' %}info{% elif error.severity == 'medium' %}warning{% endif %}">
            <div class="error-code">{{ status_code }}</div>
            <h1 class="error-title">
                {% if status_code == 404 %}
                    Page Not Found
                {% elif status_code == 403 %}
                    Access Denied
                {% elif status_code == 401 %}
                    Authentication Required
                {% elif status_code == 422 %}
                    Validation Error
                {% else %}
                    Server Error
                {% endif %}
            </h1>
        </div>
        
        <div class="error-body">
            {% if error.severity %}
            <span class="severity-badge severity-{{ error.severity }}">
                {{ error.severity }} severity
            </span>
            {% endif %}
            
            <div class="error-message">
                {{ message|default:"An unexpected error occurred. Please try again or contact support if the problem persists." }}
            </div>
            
            {% if error_id %}
            <div class="error-id">
                <strong>Error ID:</strong> {{ error_id }}
                <br>
                <small class="text-muted">Please include this ID when contacting support.</small>
            </div>
            {% endif %}
            
            <div class="error-actions">
                <a href="javascript:history.back()" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Go Back
                </a>
                <a href="{% url 'home' %}" class="btn btn-primary">
                    <i class="fas fa-home"></i> Home
                </a>
                {% if status_code == 404 %}
                <button type="button" class="btn btn-outline-info" onclick="reportBrokenLink()">
                    <i class="fas fa-exclamation-triangle"></i> Report Issue
                </button>
                {% endif %}
            </div>
            
            {% if debug %}
            <div class="debug-section">
                <h5>Debug Information</h5>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    This information is only visible in debug mode.
                </p>
                
                {% if exception_type %}
                <div class="debug-details">
                    <strong>Exception Type:</strong> {{ exception_type }}<br>
                    <strong>Exception Message:</strong> {{ exception_message }}
                </div>
                {% endif %}
                
                {% if details %}
                <div class="debug-details">
                    <strong>Error Details:</strong>
                    <pre>{{ details|pprint }}</pre>
                </div>
                {% endif %}
                
                {% if traceback %}
                <div class="debug-details">
                    <strong>Traceback:</strong>
                    <pre>{{ traceback }}</pre>
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function reportBrokenLink() {
    // Simple broken link reporting
    const errorData = {
        url: window.location.href,
        referrer: document.referrer,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        errorId: '{{ error_id|default:"" }}'
    };
    
    // You can implement actual reporting logic here
    console.log('Broken link report:', errorData);
    
    // Show user feedback
    alert('Thank you for reporting this issue. We will investigate and fix it soon.');
}

// Auto-refresh for temporary errors
{% if error.severity == 'low' and status_code >= 500 %}
setTimeout(function() {
    if (confirm('This appears to be a temporary error. Would you like to try again?')) {
        window.location.reload();
    }
}, 10000);
{% endif %}
</script>
{% endblock %}
