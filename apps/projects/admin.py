"""Enhanced Django Admin Configuration for Projects App.

Provides comprehensive admin interface for project management with:
- Organization-based data isolation
- Role-based access control
- Optimized querysets and performance
- Rich display methods with color coding
- Comprehensive filtering and search capabilities
"""

import contextlib

from django.contrib import admin
from django.db.models import Count, Q
from django.utils.html import format_html

from .models import (
    Meeting,
    Person,
    PersonCompany,
    PersonProject,
    Project,
    ProjectActivity,
    ProjectLog,
    ProjectPhase,
    ProjectTemplate,
    SavedProjectFilter,
    Stakeholder,
    Task,
    TaskTemplate,
    TaskTemplateItem,
    Workflow,
    WorkflowExecution,
)

# Unregister models if already registered to avoid conflicts
models_to_unregister = [
    Project,
    Task,
    TaskTemplate,
    TaskTemplateItem,
    Workflow,
    ProjectTemplate,
    Person,
    Stakeholder,
    ProjectPhase,
    WorkflowExecution,
]
for model in models_to_unregister:
    with contextlib.suppress(admin.sites.NotRegistered):
        admin.site.unregister(model)


# Admin Inlines
class TaskInline(admin.TabularInline):
    """Inline admin for project tasks"""

    model = Task
    extra = 0
    fields = ["title", "status", "priority", "assigned_to", "due_date"]
    show_change_link = True


class TaskTemplateItemInline(admin.TabularInline):
    """Inline admin for template items"""

    model = TaskTemplateItem
    extra = 1
    fields = [
        "order",
        "title",
        "description",
        "start_offset_days",
        "estimated_hours",
        "priority",
        "tags",
    ]
    ordering = ["order", "start_offset_days"]

    class Media:
        css = {"all": ("admin/css/forms.css",)}
        js = (
            "admin/js/jquery.init.js",
            "admin/js/inlines.js",
        )


class ProjectPersonInline(admin.TabularInline):
    """Inline admin for project persons/stakeholders"""

    model = PersonProject
    extra = 0
    fields = ["person", "role", "is_primary_contact", "start_date", "end_date"]
    autocomplete_fields = ["person"]

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "person":
            # Filter persons by organization if available
            if hasattr(request, "_obj_") and request._obj_:
                kwargs["queryset"] = Person.objects.filter(organization=request._obj_.organization)
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


class PersonProjectInline(admin.TabularInline):
    """Inline admin for Person-Project relationships"""

    model = PersonProject
    extra = 0
    fields = ["project", "role", "start_date", "end_date", "is_primary_contact"]
    autocomplete_fields = ["project"]


class PersonCompanyInline(admin.TabularInline):
    """Inline admin for Person-Company relationships"""

    model = PersonCompany
    extra = 0
    fields = [
        "company",
        "role",
        "department",
        "start_date",
        "end_date",
        "is_primary_contact",
    ]
    autocomplete_fields = ["company"]


# Main Admin Classes
@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    """Enhanced admin interface for Project model with organization isolation"""

    list_display = [
        "name",
        "client",
        "organization",
        "rag_status_colored",
        "manager_name",
        "task_count",
        "completion_percentage",
        "budget_display",
        "created_at",
    ]
    list_filter = [
        "rag_status",
        "work_type",
        "coordination_type",
        "created_at",
        "updated_at",
        "organization",
    ]
    search_fields = [
        "name",
        "client",
        "description",
        "egis_project_manager",
        "client_pm",
        "project_id_only",
    ]
    readonly_fields = [
        "created_at",
        "updated_at",
        "task_count_display",
        "conflict_count_display",
        "utility_count_display",
    ]

    fieldsets = (
        (
            "Basic Information",
            {
                "fields": (
                    "name",
                    "client",
                    "description",
                    "organization",
                    "template",
                    "work_type",
                    "coordination_type",
                )
            },
        ),
        (
            "Dates & Schedule",
            {
                "fields": (
                    "start_date",
                    "end_date",
                    "ntp_date",
                    "letting_bid_date",
                    "last_milestone",
                    "current_phase",
                )
            },
        ),
        (
            "Financial Information",
            {
                "fields": (
                    "contract_amount",
                    "hourly_rate",
                    "billed_to_date",
                    "current_cost",
                    "wip",
                    "billed_plus_wip",
                    "billed_percentage",
                    "profit_to_date",
                    "profit_percentage",
                ),
            },
        ),
        (
            "Project Management",
            {
                "fields": (
                    "rag_status",
                    "project_priority",
                    "this_month_status",
                    "status_update_date",
                    "manager_id",
                    "coordinator_id",
                    "utility_coordinator",
                ),
            },
        ),
        (
            "EGIS Information",
            {
                "fields": (
                    "egis_project_manager",
                    "egis_project_manager_email",
                    "client_pm",
                    "client_contact",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "System Information",
            {
                "fields": (
                    "created_at",
                    "updated_at",
                    "task_count_display",
                    "conflict_count_display",
                    "utility_count_display",
                ),
                "classes": ("collapse",),
            },
        ),
    )

    inlines = [TaskInline, ProjectPersonInline]
    actions = ["mark_complete", "mark_red_status", "export_project_data"]

    def get_queryset(self, request):
        """Optimize queryset with annotations and filter by access"""
        qs = super().get_queryset(request)

        # Superusers can see all projects
        if request.user.is_superuser:
            pass
        elif request.user.is_staff:
            # Use role-based access control for admin interface
            from apps.common.utils.permissions import filter_projects_by_access

            qs = filter_projects_by_access(request.user, qs)
        else:
            return qs.none()

        return qs.annotate(
            task_count=Count("tasks", distinct=True),
            completed_task_count=Count("tasks", filter=Q(tasks__status="completed"), distinct=True),
            conflict_count=Count("conflicts", distinct=True),
            utility_count=Count("utilities", distinct=True),
        ).select_related("organization")

    def rag_status_colored(self, obj):
        """Display RAG status with color coding"""
        colors = {
            "Green": "#28a745",
            "Amber": "#ffc107",
            "Red": "#dc3545",
            "Complete": "#6c757d",
        }
        color = colors.get(obj.rag_status, "#6c757d")
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.rag_status or "Unknown",
        )

    rag_status_colored.short_description = "Status"

    def manager_name(self, obj):
        """Display project manager name"""
        if obj.manager_id:
            try:
                from apps.authentication.models import User

                manager = User.objects.get(id=obj.manager_id)
                return manager.get_full_name() or manager.username
            except User.DoesNotExist:
                pass
        return obj.egis_project_manager or "Not assigned"

    manager_name.short_description = "Manager"

    def task_count(self, obj):
        """Display task count from annotation"""
        return getattr(obj, "task_count", 0)

    task_count.short_description = "Tasks"

    def completion_percentage(self, obj):
        """Display completion percentage"""
        total_tasks = getattr(obj, "task_count", 0)
        completed_tasks = getattr(obj, "completed_task_count", 0)
        if total_tasks == 0:
            return "0%"
        percentage = (completed_tasks / total_tasks) * 100
        return f"{percentage:.1f}%"

    completion_percentage.short_description = "Completion"

    def budget_display(self, obj):
        """Display budget information"""
        contract = obj.contract_amount or 0
        billed = obj.billed_to_date or 0
        if contract > 0:
            percentage = (billed / contract) * 100
            return f"${billed:,.0f} / ${contract:,.0f} ({percentage:.1f}%)"
        return f"${billed:,.0f}"

    budget_display.short_description = "Budget"

    def task_count_display(self, obj):
        """Display detailed task count"""
        total = getattr(obj, "task_count", 0)
        completed = getattr(obj, "completed_task_count", 0)
        return f"{completed} / {total} completed"

    task_count_display.short_description = "Task Summary"

    def conflict_count_display(self, obj):
        """Display conflict count"""
        return getattr(obj, "conflict_count", 0)

    conflict_count_display.short_description = "Conflicts"

    def utility_count_display(self, obj):
        """Display utility count"""
        return getattr(obj, "utility_count", 0)

    utility_count_display.short_description = "Utilities"

    # Admin Actions
    def mark_complete(self, request, queryset):
        """Mark selected projects as complete"""
        count = queryset.update(rag_status="Complete")
        self.message_user(request, f"Marked {count} projects as complete.")

    mark_complete.short_description = "Mark as complete"

    def mark_red_status(self, request, queryset):
        """Mark selected projects as red status"""
        count = queryset.update(rag_status="Red")
        self.message_user(request, f"Marked {count} projects as red status.")

    mark_red_status.short_description = "Mark as red status"

    def export_project_data(self, request, queryset):
        """Export project data to CSV format"""
        import csv

        from django.http import HttpResponse
        from django.utils import timezone

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = (
            f'attachment; filename="project_data_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'
        )

        writer = csv.writer(response)
        writer.writerow(
            [
                "Name",
                "Client",
                "Organization",
                "Status",
                "Manager",
                "Start Date",
                "End Date",
                "Contract Amount",
                "Billed to Date",
                "Current Cost",
                "Profit Percentage",
                "Task Count",
                "Completion Percentage",
                "Created At",
            ]
        )

        for project in queryset:
            writer.writerow(
                [
                    project.name,
                    project.client,
                    project.organization.name if project.organization else "",
                    project.get_rag_status_display(),
                    project.manager_name if hasattr(project, "manager_name") else "",
                    (project.start_date.strftime("%Y-%m-%d") if project.start_date else ""),
                    project.end_date.strftime("%Y-%m-%d") if project.end_date else "",
                    project.contract_amount or 0,
                    project.billed_to_date or 0,
                    project.current_cost or 0,
                    project.profit_percentage or 0,
                    project.task_count if hasattr(project, "task_count") else 0,
                    (project.completion_percentage if hasattr(project, "completion_percentage") else 0),
                    (project.created_at.strftime("%Y-%m-%d %H:%M:%S") if project.created_at else ""),
                ]
            )

        self.message_user(request, f"Successfully exported {queryset.count()} projects to CSV.")
        return response


@admin.register(Task)
class TaskAdmin(admin.ModelAdmin):
    """Enhanced admin interface for Task model"""

    list_display = [
        "title",
        "project",
        "status_colored",
        "priority_colored",
        "assigned_to_display",
        "due_date",
        "progress_bar",
        "created_at",
    ]
    list_filter = [
        "status",
        "priority",
        "project__organization",
        "due_date",
        "created_at",
        "assigned_to",
    ]
    search_fields = [
        "title",
        "description",
        "project__name",
        "assigned_to__username",
        "assigned_to__first_name",
        "assigned_to__last_name",
    ]
    readonly_fields = ["created_at", "updated_at"]

    fieldsets = (
        (
            "Task Information",
            {"fields": ("project", "title", "description", "status", "priority")},
        ),
        (
            "Assignment & Schedule",
            {
                "fields": (
                    "assigned_to",
                    "created_by",
                    "start_date",
                    "due_date",
                    "completion_date",
                )
            },
        ),
        (
            "Work Details",
            {"fields": ("estimated_hours", "actual_hours", "tags", "attachments")},
        ),
        ("Dependencies", {"fields": ("dependencies",), "classes": ("collapse",)}),
        (
            "System Information",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    filter_horizontal = ["dependencies"]
    actions = ["mark_completed", "mark_in_progress", "assign_to_me"]

    def status_colored(self, obj):
        """Display status with color coding"""
        colors = {
            "pending": "#6c757d",
            "in_progress": "#007bff",
            "completed": "#28a745",
            "cancelled": "#dc3545",
        }
        color = colors.get(obj.status, "#6c757d")
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display(),
        )

    status_colored.short_description = "Status"

    def priority_colored(self, obj):
        """Display priority with color coding"""
        colors = {
            "low": "#28a745",
            "medium": "#ffc107",
            "high": "#fd7e14",
            "urgent": "#dc3545",
        }
        color = colors.get(obj.priority, "#6c757d")
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_priority_display(),
        )

    priority_colored.short_description = "Priority"

    def progress_bar(self, obj):
        """Display progress as a percentage"""
        progress = getattr(obj, "progress_percentage", 0)
        return f"{progress}%"

    progress_bar.short_description = "Progress"

    def assigned_to_display(self, obj):
        """Display assigned users for ManyToMany field"""
        users = obj.assigned_to.all()
        if not users:
            return format_html('<span style="color: #6c757d;">Unassigned</span>')

        user_list = [user.get_full_name() or user.username for user in users[:3]]
        if len(users) > 3:
            user_list.append(f"... and {len(users) - 3} more")

        return format_html(", ".join(user_list))

    assigned_to_display.short_description = "Assigned To"

    # Admin Actions
    def mark_completed(self, request, queryset):
        """Mark selected tasks as completed"""
        from django.utils import timezone

        count = queryset.update(status="completed", completion_date=timezone.now())
        self.message_user(request, f"Marked {count} tasks as completed.")

    mark_completed.short_description = "Mark as completed"

    def mark_in_progress(self, request, queryset):
        """Mark selected tasks as in progress"""
        count = queryset.update(status="in_progress")
        self.message_user(request, f"Marked {count} tasks as in progress.")

    mark_in_progress.short_description = "Mark as in progress"

    def assign_to_me(self, request, queryset):
        """Assign selected tasks to current user"""
        count = queryset.update(assigned_to=request.user)
        self.message_user(request, f"Assigned {count} tasks to you.")

    assign_to_me.short_description = "Assign to me"


@admin.register(Person)
class PersonAdmin(admin.ModelAdmin):
    """Comprehensive admin interface for Person model - Master Stakeholder Directory"""

    list_display = [
        "full_name_colored",
        "contact_company",
        "person_type",
        "email",
        "mobile_phone",
        "importance_badge",
        "active_projects_count",
        "last_contact_display",
        "is_active",
        "created_at",
    ]
    list_filter = [
        "person_type",
        "importance_level",
        "is_active",
        "organization",
        "created_at",
        "last_contact_date",
        "next_follow_up_date",
        ("projects", admin.RelatedOnlyFieldListFilter),
        ("companies", admin.RelatedOnlyFieldListFilter),
    ]
    search_fields = [
        "full_name",
        "first_name",
        "last_name",
        "nickname",
        "email",
        "secondary_email",
        "contact_company",
        "job_title",
        "department",
        "mobile_phone",
        "business_phone",
        "tags",
    ]
    readonly_fields = [
        "created_at",
        "updated_at",
        "search_vector",
        "display_name",
        "contact_info_display",
        "active_projects_display",
        "version_history_link",
    ]

    date_hierarchy = "created_at"
    ordering = ["full_name"]

    fieldsets = [
        (
            "Basic Information",
            {
                "fields": [
                    "full_name",
                    ("first_name", "middle_name", "last_name"),
                    ("title", "suffix", "nickname"),
                    "display_name",
                    "person_type",
                    "importance_level",
                    "is_active",
                ],
            },
        ),
        (
            "Professional Information",
            {
                "fields": [
                    "job_title",
                    "department",
                    "contact_company",
                    "company_abbreviation",
                    "expertise",
                    "certifications",
                ],
            },
        ),
        (
            "Contact Information",
            {
                "fields": [
                    ("email", "secondary_email"),
                    ("mobile_phone", "business_phone"),
                    ("home_phone", "fax_number"),
                    "preferred_contact_method",
                    "contact_info_display",
                ],
            },
        ),
        (
            "Primary Address",
            {
                "fields": [
                    "address",
                    "address_line2",
                    ("city", "state", "zip_code"),
                    "country",
                ],
                "classes": ["collapse"],
            },
        ),
        (
            "Notes & Tags",
            {"fields": ["notes", "internal_notes", "tags", "custom_fields"]},
        ),
        (
            "Lifecycle & Tracking",
            {
                "fields": [
                    "date_of_birth",
                    "anniversary_date",
                    ("last_contact_date", "next_follow_up_date"),
                    "active_projects_display",
                ],
                "classes": ["collapse"],
            },
        ),
        (
            "System Information",
            {
                "fields": [
                    "organization",
                    "user_account",
                    "created_by",
                    ("created_at", "updated_at"),
                    "search_vector",
                    "display_order",
                    "version_history_link",
                ],
                "classes": ["collapse"],
            },
        ),
    ]

    inlines = [PersonProjectInline, PersonCompanyInline]
    actions = [
        "mark_as_vip",
        "mark_as_active",
        "mark_as_inactive",
        "schedule_follow_up",
        "export_contacts",
    ]
    autocomplete_fields = ["organization"]

    def get_queryset(self, request):
        """Optimize queryset with annotations and prefetches"""
        qs = super().get_queryset(request)
        return (
            qs.annotate(
                active_projects_count=Count(
                    "projects",
                    filter=Q(personproject__project__rag_status__in=["Green", "Amber", "Red"]),
                    distinct=True,
                ),
            )
            .select_related("organization", "user_account", "created_by")
            .prefetch_related("projects", "companies")
        )

    def full_name_colored(self, obj):
        """Display full name with importance color coding"""
        colors = {
            "vip": "#dc3545",
            "high": "#fd7e14",
            "medium": "#ffc107",
            "low": "#6c757d",
        }
        color = colors.get(obj.importance_level, "#000000")
        icon = "⭐" if obj.importance_level == "vip" else ""
        return format_html(
            '<span style="color: {}; font-weight: bold;">{} {}</span>',
            color,
            icon,
            obj.full_name,
        )

    full_name_colored.short_description = "Name"
    full_name_colored.admin_order_field = "full_name"

    def importance_badge(self, obj):
        """Display importance level as a badge"""
        badges = {
            "vip": '<span style="background-color: #dc3545; color: white; padding: 2px 8px; border-radius: 3px;">VIP</span>',
            "high": '<span style="background-color: #fd7e14; color: white; padding: 2px 8px; border-radius: 3px;">High</span>',
            "medium": '<span style="background-color: #ffc107; color: black; padding: 2px 8px; border-radius: 3px;">Medium</span>',
            "low": '<span style="background-color: #6c757d; color: white; padding: 2px 8px; border-radius: 3px;">Low</span>',
        }
        return format_html(badges.get(obj.importance_level, ""))

    importance_badge.short_description = "Importance"

    def active_projects_count(self, obj):
        """Display count of active projects"""
        return getattr(obj, "active_projects_count", 0)

    active_projects_count.short_description = "Active Projects"

    def last_contact_display(self, obj):
        """Display last contact date with color coding"""
        if not obj.last_contact_date:
            return format_html('<span style="color: #6c757d;">Never</span>')

        from django.utils import timezone

        days_ago = (timezone.now() - obj.last_contact_date).days

        if days_ago < 7:
            color = "#28a745"  # Green
        elif days_ago < 30:
            color = "#ffc107"  # Yellow
        else:
            color = "#dc3545"  # Red

        return format_html('<span style="color: {};">{} days ago</span>', color, days_ago)

    last_contact_display.short_description = "Last Contact"

    def contact_info_display(self, obj):
        """Display formatted contact information"""
        info = []
        if obj.email:
            info.append(f"📧 {obj.email}")
        if obj.mobile_phone:
            info.append(f"📱 {obj.mobile_phone}")
        if obj.business_phone:
            info.append(f"☎️ {obj.business_phone}")
        return format_html("<br>".join(info) if info else "No contact info")

    contact_info_display.short_description = "Contact Details"

    def active_projects_display(self, obj):
        """Display list of active projects"""
        projects = obj.get_active_projects() if hasattr(obj, "get_active_projects") else []
        if not projects:
            return "No active projects"

        project_list = [f"• {project.name}" for project in projects[:5]]
        if len(projects) > 5:
            project_list.append(f"... and {len(projects) - 5} more")

        return format_html("<br>".join(project_list))

    active_projects_display.short_description = "Active Projects"

    def version_history_link(self, obj):
        """Link to version history"""
        if obj.pk:
            from django.urls import reverse

            url = reverse("admin:versioning_versionlog_changelist") + f"?object_id={obj.pk}&content_type__model=person"
            return format_html('<a href="{}">View Version History</a>', url)
        return "Not available"

    version_history_link.short_description = "Version History"

    # Admin Actions
    def mark_as_vip(self, request, queryset):
        """Mark selected persons as VIP"""
        count = queryset.update(importance_level="vip")
        self.message_user(request, f"Marked {count} people as VIP.")

    mark_as_vip.short_description = "Mark as VIP"

    def mark_as_active(self, request, queryset):
        """Mark selected persons as active"""
        count = queryset.update(is_active=True)
        self.message_user(request, f"Marked {count} people as active.")

    mark_as_active.short_description = "Mark as active"

    def mark_as_inactive(self, request, queryset):
        """Mark selected persons as inactive"""
        count = queryset.update(is_active=False)
        self.message_user(request, f"Marked {count} people as inactive.")

    mark_as_inactive.short_description = "Mark as inactive"

    def schedule_follow_up(self, request, queryset):
        """Schedule follow-up for selected persons"""
        # from datetime import timedelta (moved to top level)

        from django.utils import timezone

        next_week = timezone.now() + timedelta(days=7)
        count = queryset.update(next_follow_up_date=next_week)
        self.message_user(request, f"Scheduled follow-up for {count} people next week.")

    schedule_follow_up.short_description = "Schedule follow-up (1 week)"

    def export_contacts(self, request, queryset):
        """Export selected contacts to CSV format"""
        import csv

        from django.http import HttpResponse
        from django.utils import timezone

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = (
            f'attachment; filename="contacts_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'
        )

        writer = csv.writer(response)
        writer.writerow(
            [
                "Full Name",
                "First Name",
                "Last Name",
                "Email",
                "Secondary Email",
                "Mobile Phone",
                "Business Phone",
                "Job Title",
                "Company",
                "Department",
                "Person Type",
                "Importance Level",
                "Address",
                "City",
                "State",
                "ZIP Code",
                "Notes",
                "Tags",
                "Last Contact Date",
                "Next Follow Up",
                "Is Active",
                "Created At",
            ]
        )

        for person in queryset:
            writer.writerow(
                [
                    person.full_name or "",
                    person.first_name or "",
                    person.last_name or "",
                    person.email or "",
                    person.secondary_email or "",
                    person.mobile_phone or "",
                    person.business_phone or "",
                    person.job_title or "",
                    person.contact_company or "",
                    person.department or "",
                    (
                        person.get_person_type_display()
                        if hasattr(person, "get_person_type_display")
                        else person.person_type
                    ),
                    (
                        person.get_importance_level_display()
                        if hasattr(person, "get_importance_level_display")
                        else person.importance_level
                    ),
                    person.address or "",
                    person.city or "",
                    person.state or "",
                    person.zip_code or "",
                    person.notes or "",
                    person.tags or "",
                    (person.last_contact_date.strftime("%Y-%m-%d") if person.last_contact_date else ""),
                    (person.next_follow_up_date.strftime("%Y-%m-%d") if person.next_follow_up_date else ""),
                    "Yes" if person.is_active else "No",
                    (person.created_at.strftime("%Y-%m-%d %H:%M:%S") if person.created_at else ""),
                ]
            )

        self.message_user(request, f"Successfully exported {queryset.count()} contacts to CSV.")
        return response


@admin.register(Meeting)
class MeetingAdmin(admin.ModelAdmin):
    """Admin interface for Meeting model"""

    list_display = [
        "title",
        "project",
        "scheduled_time",
        "duration_display",
        "meeting_type",
        "status_colored",
        "attendee_count",
        "created_by",
    ]
    list_filter = ["meeting_type", "status", "scheduled_time", "created_at"]
    search_fields = ["title", "description", "project__name", "location"]
    readonly_fields = ["created_at", "updated_at"]

    fieldsets = (
        (
            "Meeting Information",
            {"fields": ("title", "description", "project", "created_by")},
        ),
        ("Schedule", {"fields": ("scheduled_time", "duration")}),
        (
            "Meeting Details",
            {"fields": ("meeting_type", "location", "meeting_link", "status")},
        ),
        ("Content", {"fields": ("agenda", "notes"), "classes": ("collapse",)}),
        (
            "Recurrence",
            {
                "fields": ("is_recurring", "recurrence_pattern", "parent_meeting"),
                "classes": ("collapse",),
            },
        ),
        (
            "System Information",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    filter_horizontal = ["attendees"]

    def duration_display(self, obj):
        """Display duration in hours and minutes"""
        hours = obj.duration // 60
        minutes = obj.duration % 60
        if hours > 0:
            return f"{hours}h {minutes}m"
        return f"{minutes}m"

    duration_display.short_description = "Duration"

    def status_colored(self, obj):
        """Display status with color coding"""
        colors = {
            "scheduled": "#007bff",
            "in_progress": "#28a745",
            "completed": "#6c757d",
            "cancelled": "#dc3545",
            "postponed": "#ffc107",
        }
        color = colors.get(obj.status, "#6c757d")
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display(),
        )

    status_colored.short_description = "Status"

    def attendee_count(self, obj):
        """Display number of attendees"""
        return obj.attendees.count()

    attendee_count.short_description = "Attendees"


@admin.register(ProjectTemplate)
class ProjectTemplateAdmin(admin.ModelAdmin):
    """Admin interface for ProjectTemplate model"""

    list_display = [
        "name",
        "organization",
        "is_active",
        "is_default",
        "created_by",
        "created_at",
    ]
    list_filter = ["is_active", "is_default", "organization", "created_at"]
    search_fields = ["name", "description"]
    readonly_fields = ["created_at", "updated_at"]

    fieldsets = (
        (
            "Template Information",
            {"fields": ("name", "description", "organization", "created_by")},
        ),
        ("Template Settings", {"fields": ("icon", "color", "is_active", "is_default")}),
        (
            "Workflow Configuration",
            {"fields": ("workflow_phases", "settings"), "classes": ("collapse",)},
        ),
        (
            "System Information",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(TaskTemplate)
class TaskTemplateAdmin(admin.ModelAdmin):
    """Admin interface for TaskTemplate model"""

    list_display = [
        "template_name",
        "category_display",
        "organization",
        "usage_count_display",
        "item_count",
        "is_active",
        "created_by",
        "last_used",
    ]
    list_filter = [
        "category",
        "is_active",
        "organization",
        "default_priority",
        "created_at",
        "last_used",
    ]
    search_fields = ["template_name", "description", "tags"]
    readonly_fields = [
        "usage_count",
        "last_used",
        "created_at",
        "updated_at",
        "template_preview_display",
    ]

    fieldsets = (
        (
            "Template Information",
            {
                "fields": (
                    "template_name",
                    "description",
                    "category",
                    "tags",
                    "is_active",
                )
            },
        ),
        ("Organization & Creator", {"fields": ("organization", "created_by")}),
        (
            "Template Settings",
            {"fields": ("estimated_duration_days", "default_priority")},
        ),
        (
            "Usage Statistics",
            {"fields": ("usage_count", "last_used"), "classes": ("collapse",)},
        ),
        (
            "Template Preview",
            {
                "fields": ("template_preview_display",),
                "classes": ("collapse",),
                "description": "Preview of what tasks will be created by this template",
            },
        ),
        (
            "System Information",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    inlines = [TaskTemplateItemInline]
    actions = ["duplicate_template", "apply_to_project", "mark_inactive"]

    def get_queryset(self, request):
        """Optimize queryset with annotations"""
        qs = super().get_queryset(request)

        # Filter by organization for non-superusers
        if not request.user.is_superuser:
            if hasattr(request.user, "organization") and request.user.organization:
                qs = qs.filter(organization=request.user.organization)
            else:
                return qs.none()

        return qs.annotate(item_count=Count("template_items", distinct=True)).select_related(
            "organization", "created_by"
        )

    def category_display(self, obj):
        """Display category with icon"""
        icons = {
            "software": "💻",
            "construction": "🏗️",
            "marketing": "📢",
            "event": "🎪",
            "research": "🔬",
            "product": "📦",
            "compliance": "✅",
            "maintenance": "🔧",
            "custom": "⚙️",
        }
        icon = icons.get(obj.category, "📋")
        return format_html("{} {}", icon, obj.get_category_display())

    category_display.short_description = "Category"

    def usage_count_display(self, obj):
        """Display usage count with visual indicator"""
        count = obj.usage_count
        if count == 0:
            color = "#6c757d"
        elif count < 5:
            color = "#ffc107"
        elif count < 20:
            color = "#28a745"
        else:
            color = "#007bff"

        return format_html('<span style="color: {}; font-weight: bold;">{}</span>', color, count)

    usage_count_display.short_description = "Used"

    def item_count(self, obj):
        """Display count of template items"""
        return getattr(obj, "item_count", 0)

    item_count.short_description = "Tasks"

    def template_preview_display(self, obj):
        """Display template preview as formatted HTML"""
        if not obj.pk:
            return "Save template to see preview"

        try:
            preview = obj.get_template_preview()
            html_parts = [
                f"<strong>Template:</strong> {preview['template_name']}<br>",
                f"<strong>Category:</strong> {preview['category']}<br>",
                f"<strong>Total Tasks:</strong> {preview['total_items']}<br>",
            ]

            if preview.get("estimated_duration_days"):
                html_parts.append(f"<strong>Duration:</strong> {preview['estimated_duration_days']} days<br>")

            html_parts.append("<br><strong>Tasks:</strong><ul>")

            for item in preview["items"]:
                dependency_info = f" ({item['dependency_count']} deps)" if item["dependency_count"] > 0 else ""
                hours_info = f" - {item['estimated_hours']}h" if item.get("estimated_hours") else ""
                offset_info = f" +{item['start_offset_days']}d" if item["start_offset_days"] > 0 else ""

                html_parts.append(
                    f"<li><strong>{item['title']}</strong>{hours_info}{offset_info}{dependency_info}</li>"
                )

            html_parts.append("</ul>")

            return format_html("".join(html_parts))

        except Exception as e:
            return f"Error generating preview: {str(e)}"

    template_preview_display.short_description = "Template Preview"

    # Admin Actions
    def duplicate_template(self, request, queryset):
        """Duplicate selected templates"""
        count = 0
        for template in queryset:
            # Create copy of template
            new_template = TaskTemplate.objects.create(
                template_name=f"{template.template_name} (Copy)",
                description=template.description,
                category=template.category,
                tags=template.tags.copy() if template.tags else [],
                organization=template.organization,
                created_by=request.user,
                estimated_duration_days=template.estimated_duration_days,
                default_priority=template.default_priority,
                is_active=False,  # New copies start inactive
            )

            # Copy all template items
            for item in template.template_items.all():
                TaskTemplateItem.objects.create(
                    template=new_template,
                    title=item.title,
                    description=item.description,
                    order=item.order,
                    start_offset_days=item.start_offset_days,
                    estimated_hours=item.estimated_hours,
                    priority=item.priority,
                    tags=item.tags.copy() if item.tags else [],
                    dependency_template_item_ids=(
                        item.dependency_template_item_ids.copy() if item.dependency_template_item_ids else []
                    ),
                )

            count += 1

        self.message_user(request, f"Successfully duplicated {count} templates.")

    duplicate_template.short_description = "Duplicate selected templates"

    def apply_to_project(self, request, queryset):
        """Redirect to template application form"""
        if queryset.count() != 1:
            self.message_user(request, "Please select exactly one template to apply.", level="warning")
            return

        # In a real implementation, this would redirect to a custom view
        self.message_user(
            request,
            "Template application feature would be implemented in custom views.",
        )

    apply_to_project.short_description = "Apply template to project"

    def mark_inactive(self, request, queryset):
        """Mark selected templates as inactive"""
        count = queryset.update(is_active=False)
        self.message_user(request, f"Marked {count} templates as inactive.")

    mark_inactive.short_description = "Mark as inactive"


@admin.register(TaskTemplateItem)
class TaskTemplateItemAdmin(admin.ModelAdmin):
    """Admin interface for TaskTemplateItem model"""

    list_display = [
        "title",
        "template",
        "order",
        "start_offset_days",
        "estimated_hours",
        "priority_display",
        "dependency_count",
        "tags_display",
    ]
    list_filter = [
        "template__category",
        "template__organization",
        "priority",
        "start_offset_days",
        "template",
    ]
    search_fields = ["title", "description", "template__template_name"]
    readonly_fields = ["created_at", "updated_at", "dependency_preview"]

    fieldsets = (
        ("Task Definition", {"fields": ("template", "title", "description", "order")}),
        ("Scheduling", {"fields": ("start_offset_days", "estimated_hours")}),
        ("Task Properties", {"fields": ("priority", "tags")}),
        (
            "Dependencies",
            {
                "fields": ("dependency_template_item_ids", "dependency_preview"),
                "description": "Enter template item IDs that this task depends on",
            },
        ),
        (
            "System Information",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    ordering = ["template", "order", "start_offset_days"]

    def get_queryset(self, request):
        """Optimize queryset"""
        qs = super().get_queryset(request)
        return qs.select_related("template", "template__organization")

    def priority_display(self, obj):
        """Display priority with color coding"""
        if not obj.priority:
            return format_html('<span style="color: #6c757d;">Default</span>')

        colors = {
            "low": "#28a745",
            "medium": "#ffc107",
            "high": "#fd7e14",
            "critical": "#dc3545",
        }
        color = colors.get(obj.priority, "#6c757d")
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_priority_display(),
        )

    priority_display.short_description = "Priority"

    def dependency_count(self, obj):
        """Display number of dependencies"""
        count = len(obj.dependency_template_item_ids) if obj.dependency_template_item_ids else 0
        if count == 0:
            return format_html('<span style="color: #6c757d;">None</span>')
        return format_html('<span style="color: #007bff; font-weight: bold;">{}</span>', count)

    dependency_count.short_description = "Dependencies"

    def tags_display(self, obj):
        """Display tags as badges"""
        if not obj.tags:
            return format_html('<span style="color: #6c757d;">None</span>')

        badges = []
        for tag in obj.tags[:3]:  # Show first 3 tags
            badges.append(
                f'<span style="background-color: #e9ecef; color: #495057; '
                f'padding: 2px 6px; border-radius: 3px; font-size: 11px;">{tag}</span>'
            )

        if len(obj.tags) > 3:
            badges.append(f'<span style="color: #6c757d;">+{len(obj.tags) - 3} more</span>')

        return format_html(" ".join(badges))

    tags_display.short_description = "Tags"

    def dependency_preview(self, obj):
        """Show preview of dependency items"""
        if not obj.dependency_template_item_ids or not obj.template_id:
            return "No dependencies"

        try:
            dep_items = obj.get_dependency_items()
            if not dep_items.exists():
                return "Invalid dependency references"

            dep_list = []
            for dep in dep_items:
                dep_list.append(f"• {dep.title} (Order: {dep.order})")

            return format_html("<br>".join(dep_list))

        except Exception as e:
            return f"Error: {str(e)}"

    dependency_preview.short_description = "Dependency Items"


# Register additional models with basic admin
@admin.register(ProjectPhase)
class ProjectPhaseAdmin(admin.ModelAdmin):
    """Admin configuration for ProjectPhase"""

    list_display = ["name", "project", "order", "status", "start_date", "end_date"]
    list_filter = ["status", "start_date", "end_date"]
    search_fields = ["name", "description", "project__name"]
    ordering = ["project", "order"]


@admin.register(Workflow)
class WorkflowAdmin(admin.ModelAdmin):
    """Admin configuration for Workflow"""

    list_display = ["name", "is_active", "created_by", "created_at"]
    list_filter = ["is_active", "created_at"]
    search_fields = ["name", "description"]
    ordering = ["-created_at"]


@admin.register(WorkflowExecution)
class WorkflowExecutionAdmin(admin.ModelAdmin):
    """Admin configuration for WorkflowExecution"""

    list_display = ["workflow", "project", "current_step", "status", "started_at"]
    list_filter = ["status", "started_at", "workflow"]
    search_fields = ["current_step", "error_message"]
    ordering = ["-started_at"]


# Register basic models
admin.site.register(ProjectActivity)
admin.site.register(ProjectLog)


@admin.register(SavedProjectFilter)
class SavedProjectFilterAdmin(admin.ModelAdmin):
    """Admin interface for SavedProjectFilter model."""

    list_display = ["name", "user", "is_shared", "use_count", "last_used", "created_at"]
    list_filter = ["is_shared", "created_at", "last_used"]
    search_fields = ["name", "description", "user__username", "user__email"]
    readonly_fields = ["use_count", "last_used", "created_at", "updated_at"]
    ordering = ["-last_used", "-created_at"]

    fieldsets = (
        (None, {"fields": ("user", "name", "description")}),
        ("Filter Configuration", {"fields": ("query_string",)}),
        (
            "Sharing",
            {
                "fields": ("is_shared",),
                "description": "Shared filters are available to all organization members.",
            },
        ),
        (
            "Usage Statistics",
            {"fields": ("use_count", "last_used"), "classes": ("collapse",)},
        ),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def get_queryset(self, request):
        """Filter saved filters by user's organization."""
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        return qs.filter(user__organization=request.user.organization)


# Customize admin site headers
admin.site.site_header = "CLEAR Project Management Admin"
admin.site.site_title = "CLEAR Admin"
admin.site.index_title = "Project Management Administration"


# Note: OrganizationMember and ProjectMember have composite primary keys
# and cannot be registered with Django admin. These should be managed
# through custom interfaces or the Organization/Project admin interfaces.
