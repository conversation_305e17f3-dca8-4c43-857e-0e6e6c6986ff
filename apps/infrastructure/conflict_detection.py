"""Advanced 3D Utility Conflict Detection System

from dataclasses import dataclass
from datetime import datetime
from django.core.exceptions import ValidationError
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from rest_framework import status
from typing import ClassVar
from typing import List
import logging
logger = logging.getLogger(__name__)
import os
import re
import time

This module provides comprehensive 3D spatial conflict detection for utility infrastructure
using PostGIS 3D operations, industry-standard clearance requirements, and risk assessment.
"""

import logging
import math
from dataclasses import dataclass
from datetime import datetime
from typing import ClassVar

from django.contrib.gis.geos import GEOSGeometry, Point
from django.utils import timezone as django_timezone
from django.utils.translation import gettext_lazy as _

from .spatial_operations import PostGISSpatialOperations, UtilitySpatialAnalyzer

logger = logging.getLogger(__name__)


@dataclass
class ConflictResult:
    """Data class for conflict detection results"""

    conflict_id: str
    line1_id: int
    line2_id: int
    conflict_type: str
    severity: str
    distance: float
    intersection_point: Point | None
    clearance_violation: float
    risk_score: float
    apwa_compliant: bool
    recommendations: list[str]
    metadata: dict


@dataclass
class SpatialClearance:
    """Industry-standard spatial clearance requirements"""

    horizontal_clearance: float  # meters
    vertical_clearance: float  # meters
    crossing_angle_min: float  # degrees
    parallel_separation: float  # meters
    standard_reference: str  # APWA, ASCE, etc.


class ThreeDConflictDetector:
    """Advanced 3D conflict detection engine for utility infrastructure.

    Supports:
    - 3D spatial analysis with elevation data
    - Industry-standard clearance requirements (APWA, ASCE, AWWA)
    - Risk-based conflict assessment
    - Real-time conflict monitoring
    - Predictive conflict analysis
    """

    # Industry-standard clearances (in meters)
    CLEARANCE_STANDARDS: ClassVar[dict] = {
        # High-risk utility combinations requiring greater separation
        ("gas", "electric"): SpatialClearance(
            horizontal_clearance=0.6,  # 24 inches
            vertical_clearance=0.3,  # 12 inches
            crossing_angle_min=30.0,  # 30 degrees minimum
            parallel_separation=1.0,  # 3 feet for parallel runs
            standard_reference="APWA-2021",
        ),
        ("gas", "water"): SpatialClearance(
            horizontal_clearance=0.3,  # 12 inches
            vertical_clearance=0.15,  # 6 inches
            crossing_angle_min=45.0,
            parallel_separation=0.6,  # 24 inches
            standard_reference="APWA-2021",
        ),
        ("gas", "sewer"): SpatialClearance(
            horizontal_clearance=0.3,  # 12 inches
            vertical_clearance=0.15,  # 6 inches
            crossing_angle_min=30.0,
            parallel_separation=0.6,
            standard_reference="APWA-2021",
        ),
        ("electric", "water"): SpatialClearance(
            horizontal_clearance=0.3,  # 12 inches
            vertical_clearance=0.15,  # 6 inches
            crossing_angle_min=45.0,
            parallel_separation=0.45,  # 18 inches
            standard_reference="IEEE-837",
        ),
        ("electric", "sewer"): SpatialClearance(
            horizontal_clearance=0.3,  # 12 inches
            vertical_clearance=0.15,  # 6 inches
            crossing_angle_min=30.0,
            parallel_separation=0.45,
            standard_reference="IEEE-837",
        ),
        ("water", "sewer"): SpatialClearance(
            horizontal_clearance=3.0,  # 10 feet horizontal
            vertical_clearance=0.45,  # 18 inches vertical
            crossing_angle_min=45.0,
            parallel_separation=3.0,  # 10 feet for parallel runs
            standard_reference="AWWA-C651",
        ),
        ("telecom", "electric"): SpatialClearance(
            horizontal_clearance=0.3,  # 12 inches
            vertical_clearance=0.15,  # 6 inches
            crossing_angle_min=30.0,
            parallel_separation=0.3,
            standard_reference="TIA-758",
        ),
        # Default clearances for other combinations
        "default": SpatialClearance(
            horizontal_clearance=0.15,  # 6 inches
            vertical_clearance=0.1,  # 4 inches
            crossing_angle_min=30.0,
            parallel_separation=0.3,  # 12 inches
            standard_reference="General",
        ),
    }

    def __init__(self):
        self.spatial_ops = PostGISSpatialOperations()
        self.analyzer = UtilitySpatialAnalyzer()

    def detect_3d_conflicts(
        self,
        utility_lines_queryset,
        include_elevation: bool = True,
        risk_threshold: float = 0.7,
        parallel_check_distance: float = 100.0,
    ) -> list[ConflictResult]:
        """Comprehensive 3D conflict detection with elevation analysis.

        Args:
        ----
            utility_lines_queryset: QuerySet of UtilityLineData objects
            include_elevation: Include elevation-based 3D analysis
            risk_threshold: Minimum risk score to report conflicts
            parallel_check_distance: Distance to check for parallel utilities

        Returns:
        -------
            List of ConflictResult objects

        """
        conflicts = []

        try:
            # Get utility lines with spatial data
            lines = list(utility_lines_queryset.filter(geometry__isnull=False).select_related("utility"))

            logger.info(_(f"Analyzing {len(lines)} utility lines for 3D conflicts"))

            # Check each pair of utility lines
            for i, line1 in enumerate(lines):
                for line2 in lines[i + 1 :]:
                    # Skip if same utility or organization
                    if line1.utility_id == line2.utility_id:
                        continue

                    # Perform 3D conflict analysis
                    conflict = self._analyze_3d_utility_conflict(
                        line1,
                        line2,
                        include_elevation,
                        parallel_check_distance,
                    )

                    if conflict and conflict.risk_score >= risk_threshold:
                        conflicts.append(conflict)

            # Sort by risk score (highest first)
            conflicts.sort(key=lambda x: x.risk_score, reverse=True)

            logger.info(_(f"Detected {len(conflicts)} conflicts above risk threshold {risk_threshold}"))

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.error(_(f"Error in 3D conflict detection: {e}"))

        return conflicts

    def _analyze_3d_utility_conflict(
        self,
        line1,
        line2,
        include_elevation: bool,
        parallel_check_distance: float,
    ) -> ConflictResult | None:
        """Analyze potential 3D conflict between two utility lines"""
        try:
            # Ensure geometries are in SRID 4326
            geom1 = PostGISSpatialOperations.ensure_srid_4326(line1.geometry)
            geom2 = PostGISSpatialOperations.ensure_srid_4326(line2.geometry)

            # Calculate basic spatial relationship
            distance_2d = geom1.distance(geom2)

            # Get clearance requirements for this utility pair
            clearance = self._get_clearance_requirements(line1.utility_type, line2.utility_type)

            # Check if utilities are close enough to require analysis
            if distance_2d > clearance.horizontal_clearance * 5:  # 5x clearance as analysis threshold
                return None

            # Determine conflict type and analyze geometry
            conflict_type = self._determine_conflict_type(geom1, geom2, parallel_check_distance)

            # Calculate intersection point
            intersection = geom1.intersection(geom2)
            intersection_point = None
            if not intersection.empty:
                if hasattr(intersection, "coords"):
                    coords = list(intersection.coords)
                    if coords:
                        intersection_point = Point(coords[0], srid=4326)
                elif hasattr(intersection, "centroid"):
                    intersection_point = intersection.centroid

            # 3D elevation analysis
            elevation_data = {}
            if include_elevation:
                elevation_data = self._analyze_elevation_conflict(line1, line2, clearance)

            # Calculate actual clearance violation
            if conflict_type == "crossing":
                clearance_violation = max(0, clearance.horizontal_clearance - distance_2d)
            elif conflict_type == "parallel":
                clearance_violation = max(0, clearance.parallel_separation - distance_2d)
            else:
                clearance_violation = max(0, clearance.horizontal_clearance - distance_2d)

            # Add vertical clearance violation if elevation data available
            if elevation_data.get("vertical_separation") is not None:
                vertical_violation = max(
                    0,
                    clearance.vertical_clearance - elevation_data["vertical_separation"],
                )
                clearance_violation = max(clearance_violation, vertical_violation)

            # Skip if no violation
            if clearance_violation <= 0:
                return None

            # Calculate risk score
            risk_score = self._calculate_3d_risk_score(
                line1,
                line2,
                distance_2d,
                clearance_violation,
                conflict_type,
                elevation_data,
            )

            # Determine severity
            severity = self._determine_conflict_severity(risk_score, clearance_violation, clearance)

            # Check APWA compliance
            apwa_compliant = self._check_apwa_compliance(line1, line2, distance_2d, elevation_data)

            # Generate recommendations
            recommendations = self._generate_conflict_recommendations(
                line1,
                line2,
                conflict_type,
                clearance_violation,
                elevation_data,
            )

            # Create conflict result
            conflict_id = f"{line1.id}-{line2.id}-{datetime.now().strftime('%Y%m%d%H%M%S')}"

            return ConflictResult(
                conflict_id=conflict_id,
                line1_id=line1.id,
                line2_id=line2.id,
                conflict_type=conflict_type,
                severity=severity,
                distance=distance_2d,
                intersection_point=intersection_point,
                clearance_violation=clearance_violation,
                risk_score=risk_score,
                apwa_compliant=apwa_compliant,
                recommendations=recommendations,
                metadata={
                    "line1_type": line1.utility_type,
                    "line2_type": line2.utility_type,
                    "clearance_standard": clearance.standard_reference,
                    "elevation_data": elevation_data,
                    "analysis_timestamp": django_timezone.now().isoformat(),
                },
            )

        except (ValidationError, ValueError) as e:
            logger.error(_(f"Error analyzing conflict between lines {line1.id} and {line2.id}: {e}"))
            return None

    def _get_clearance_requirements(self, utility_type1: str, utility_type2: str) -> SpatialClearance:
        """Get clearance requirements for utility pair"""
        utility_pair = tuple(sorted([utility_type1.lower(), utility_type2.lower()]))
        return self.CLEARANCE_STANDARDS.get(utility_pair, self.CLEARANCE_STANDARDS["default"])

    def _determine_conflict_type(self, geom1: GEOSGeometry, geom2: GEOSGeometry, parallel_check_distance: float) -> str:
        """Determine the type of spatial conflict"""
        # Check if geometries intersect
        if geom1.intersects(geom2):
            return "crossing"

        # Check if lines are parallel (for LineString geometries)
        if hasattr(geom1, "coords") and hasattr(geom2, "coords"):
            try:
                # Sample points along lines to check parallelism
                if len(list(geom1.coords)) >= 2 and len(list(geom2.coords)) >= 2:
                    # Calculate bearing/direction of each line
                    line1_coords = list(geom1.coords)
                    line2_coords = list(geom2.coords)

                    # Simple parallelism check using first and last points
                    line1_bearing = self._calculate_bearing(line1_coords[0], line1_coords[-1])
                    line2_bearing = self._calculate_bearing(line2_coords[0], line2_coords[-1])

                    bearing_diff = abs(line1_bearing - line2_bearing)
                    if bearing_diff > 180:
                        bearing_diff = 360 - bearing_diff

                    # If bearings are similar (within 30 degrees), consider parallel
                    if bearing_diff < 30 or bearing_diff > 150:
                        # Check if they're close enough to be considered parallel
                        if geom1.distance(geom2) < parallel_check_distance:
                            return "parallel"
            except (AttributeError, KeyError, ValueError, TypeError):
                pass  # Fall through to proximity check

        # Default to proximity conflict
        return "proximity"

    def _calculate_bearing(self, point1: tuple[float, float], point2: tuple[float, float]) -> float:
        """Calculate bearing between two points in degrees"""
        lat1, lon1 = math.radians(point1[1]), math.radians(point1[0])
        lat2, lon2 = math.radians(point2[1]), math.radians(point2[0])

        dlon = lon2 - lon1

        y = math.sin(dlon) * math.cos(lat2)
        x = math.cos(lat1) * math.sin(lat2) - math.sin(lat1) * math.cos(lat2) * math.cos(dlon)

        bearing = math.atan2(y, x)
        bearing = math.degrees(bearing)
        return (bearing + 360) % 360

    def _analyze_elevation_conflict(self, line1, line2, clearance: SpatialClearance) -> dict:
        """Analyze elevation-based 3D conflicts"""
        elevation_data = {
            "has_elevation_data": False,
            "vertical_separation": None,
            "line1_elevation": None,
            "line2_elevation": None,
            "line1_depth": None,
            "line2_depth": None,
            "vertical_conflict": False,
        }

        try:
            # Get elevation data from line properties or utility
            line1_props = getattr(line1, "properties", {})
            line2_props = getattr(line2, "properties", {})

            line1_elevation = line1_props.get("elevation")
            line2_elevation = line2_props.get("elevation")

            # Get installation depth from utility
            line1_depth = None
            line2_depth = None

            if hasattr(line1, "utility") and line1.utility:
                line1_depth = getattr(line1.utility, "installation_depth", None)
            if hasattr(line2, "utility") and line2.utility:
                line2_depth = getattr(line2.utility, "installation_depth", None)

            # If we have elevation data, calculate 3D relationship
            if line1_elevation is not None and line2_elevation is not None:
                elevation_data["has_elevation_data"] = True
                elevation_data["line1_elevation"] = line1_elevation
                elevation_data["line2_elevation"] = line2_elevation
                elevation_data["line1_depth"] = line1_depth or 0
                elevation_data["line2_depth"] = line2_depth or 0

                # Calculate actual depths (elevation - installation depth)
                line1_actual_depth = line1_elevation - (line1_depth or 0)
                line2_actual_depth = line2_elevation - (line2_depth or 0)

                # Calculate vertical separation
                vertical_separation = abs(line1_actual_depth - line2_actual_depth)
                elevation_data["vertical_separation"] = vertical_separation

                # Check for vertical conflict
                elevation_data["vertical_conflict"] = vertical_separation < clearance.vertical_clearance

                # Additional 3D analysis
                elevation_data["depth_difference"] = abs((line1_depth or 0) - (line2_depth or 0))
                elevation_data["same_depth_range"] = elevation_data["depth_difference"] < 0.5  # Within 0.5m

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.warning(_(f"Error in elevation analysis: {e}"))

        return elevation_data

    def _calculate_3d_risk_score(
        self,
        line1,
        line2,
        distance_2d: float,
        clearance_violation: float,
        conflict_type: str,
        elevation_data: dict,
    ) -> float:
        """Calculate comprehensive 3D risk score (0.0 to 1.0)"""
        risk_factors = {
            "clearance_violation": 0.0,
            "utility_compatibility": 0.0,
            "conflict_geometry": 0.0,
            "installation_risk": 0.0,
            "operational_risk": 0.0,
            "elevation_risk": 0.0,
        }

        try:
            # 1. Clearance violation risk (40% weight)
            clearance = self._get_clearance_requirements(line1.utility_type, line2.utility_type)
            if clearance_violation > 0:
                violation_ratio = clearance_violation / clearance.horizontal_clearance
                risk_factors["clearance_violation"] = min(1.0, violation_ratio)

            # 2. Utility type compatibility risk (25% weight)
            high_risk_pairs = [
                ("gas", "electric"),
                ("gas", "water"),
                ("water", "sewer"),
            ]
            utility_pair = tuple(sorted([line1.utility_type.lower(), line2.utility_type.lower()]))
            if utility_pair in high_risk_pairs:
                risk_factors["utility_compatibility"] = 0.8
            else:
                risk_factors["utility_compatibility"] = 0.3

            # 3. Conflict geometry risk (15% weight)
            if conflict_type == "crossing":
                risk_factors["conflict_geometry"] = 0.9  # Crossings are highest risk
            elif conflict_type == "parallel":
                risk_factors["conflict_geometry"] = 0.6  # Parallel runs have medium risk
            else:
                risk_factors["conflict_geometry"] = 0.4  # Proximity has lower risk

            # 4. Installation depth risk (10% weight)
            if elevation_data.get("has_elevation_data"):
                line1_depth = elevation_data.get("line1_depth", 0) or 0
                line2_depth = elevation_data.get("line2_depth", 0) or 0

                # Shallow installations are riskier
                min_depth = min(line1_depth, line2_depth)
                if min_depth < 0.6:  # Less than 2 feet
                    risk_factors["installation_risk"] = 0.8
                elif min_depth < 1.2:  # Less than 4 feet
                    risk_factors["installation_risk"] = 0.5
                else:
                    risk_factors["installation_risk"] = 0.2

            # 5. Operational risk factors (5% weight)
            operational_risk = 0.3  # Default

            # High pressure gas or water systems
            if "gas" in [line1.utility_type, line2.utility_type]:
                gas_pressure = self._get_utility_pressure(line1, line2, "gas")
                if gas_pressure and gas_pressure > 60:  # High pressure gas (>60 psi)
                    operational_risk = 0.8

            # High voltage electrical systems
            if "electric" in [line1.utility_type, line2.utility_type]:
                voltage = self._get_utility_voltage(line1, line2)
                if voltage and voltage > 15000:  # >15kV
                    operational_risk = 0.9

            risk_factors["operational_risk"] = operational_risk

            # 6. Elevation-specific risk (5% weight)
            if elevation_data.get("vertical_conflict"):
                risk_factors["elevation_risk"] = 0.9
            elif elevation_data.get("same_depth_range"):
                risk_factors["elevation_risk"] = 0.6
            else:
                risk_factors["elevation_risk"] = 0.2

            # Calculate weighted risk score
            weights = {
                "clearance_violation": 0.4,
                "utility_compatibility": 0.25,
                "conflict_geometry": 0.15,
                "installation_risk": 0.1,
                "operational_risk": 0.05,
                "elevation_risk": 0.05,
            }

            total_risk = sum(risk_factors[factor] * weights[factor] for factor in risk_factors)

            return min(1.0, total_risk)

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.error(_(f"Error calculating risk score: {e}"))
            return 0.5  # Default medium risk

    def _get_utility_pressure(self, line1, line2, utility_type: str) -> float | None:
        """Get utility operating pressure from properties"""
        for line in [line1, line2]:
            if line.utility_type.lower() == utility_type:
                props = getattr(line, "properties", {})
                pressure = props.get("operating_pressure") or props.get("pressure")
                if pressure:
                    return float(pressure)
        return None

    def _get_utility_voltage(self, line1, line2) -> float | None:
        """Get electrical utility voltage from properties"""
        for line in [line1, line2]:
            if line.utility_type.lower() == "electric":
                props = getattr(line, "properties", {})
                voltage = props.get("voltage") or props.get("operating_voltage")
                if voltage:
                    return float(voltage)
        return None

    def _determine_conflict_severity(
        self,
        risk_score: float,
        clearance_violation: float,
        clearance: SpatialClearance,
    ) -> str:
        """Determine conflict severity based on risk and violation"""
        if risk_score >= 0.8:
            return "critical"
        if risk_score >= 0.6:
            return "high"
        if risk_score >= 0.4:
            return "medium"
        return "low"

    def _check_apwa_compliance(self, line1, line2, distance_2d: float, elevation_data: dict) -> bool:
        """Check if configuration meets APWA standards"""
        try:
            clearance = self._get_clearance_requirements(line1.utility_type, line2.utility_type)

            # Check horizontal clearance
            if distance_2d < clearance.horizontal_clearance:
                return False

            # Check vertical clearance if elevation data available
            if elevation_data.get("vertical_separation") is not None:
                if elevation_data["vertical_separation"] < clearance.vertical_clearance:
                    return False

            return True

        except (AttributeError, KeyError, ValueError, TypeError):
            return False

    def _generate_conflict_recommendations(
        self,
        line1,
        line2,
        conflict_type: str,
        clearance_violation: float,
        elevation_data: dict,
    ) -> list[str]:
        """Generate actionable recommendations for conflict resolution"""
        recommendations = []

        try:
            clearance = self._get_clearance_requirements(line1.utility_type, line2.utility_type)

            # General recommendations based on conflict type
            if conflict_type == "crossing":
                recommendations.append(_(f"Maintain minimum {clearance.crossing_angle_min}° crossing angle"))
                recommendations.append(_(f"Ensure {clearance.vertical_clearance}m vertical clearance at crossing"))
            elif conflict_type == "parallel":
                recommendations.append(
                    _(f"Maintain {clearance.parallel_separation}m separation for parallel utilities"),
                )
                recommendations.append(_("Consider relocating one utility to meet clearance requirements"))

            # Specific utility pair recommendations
            utility_pair = tuple(sorted([line1.utility_type.lower(), line2.utility_type.lower()]))

            if utility_pair == ("gas", "electric"):
                recommendations.extend(
                    [
                        _("Install warning tape above gas line"),
                        _("Use protective sleeves where utilities cross"),
                        _("Coordinate with both utilities for maintenance access"),
                    ],
                )
            elif utility_pair == ("water", "sewer"):
                recommendations.extend(
                    [
                        _("Water line must be at least 10 feet horizontally from sewer"),
                        _("If crossing, water line should be above sewer line"),
                        _("Use ductile iron pipe for water line in conflict areas"),
                    ],
                )
            elif "gas" in utility_pair:
                recommendations.extend(
                    [
                        _("Install cathodic protection if near metallic utilities"),
                        _("Use PE pipe where possible to reduce corrosion risk"),
                        _("Mark location with permanent markers"),
                    ],
                )

            # Elevation-based recommendations
            if elevation_data.get("vertical_conflict"):
                recommendations.append(_(f"Increase vertical separation to minimum {clearance.vertical_clearance}m"))
                recommendations.append(_("Consider adjusting installation depth of one utility"))

            # High-risk situation recommendations
            if clearance_violation > clearance.horizontal_clearance * 0.5:
                recommendations.extend(
                    [
                        _("Immediate engineering review required"),
                        _("Consider utility relocation or specialized construction methods"),
                        _("Implement enhanced monitoring during construction"),
                    ],
                )

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.error(_(f"Error generating recommendations: {e}"))
            recommendations.append(_("Contact utilities coordination office for guidance"))

        return recommendations

    def create_conflict_model_instance(self, conflict_result: ConflictResult, project):
        """Create a Conflict model instance from ConflictResult"""
        try:
            from .models import Conflict

            # Map severity to priority
            priority_map = {
                "critical": "critical",
                "high": "high",
                "medium": "medium",
                "low": "low",
            }

            return Conflict.objects.create(
                project=project,
                description=_(
                    f"{conflict_result.conflict_type.title()} conflict between "
                    f"{conflict_result.metadata['line1_type']} and "
                    f"{conflict_result.metadata['line2_type']} utilities",
                ),
                status="open",
                priority=priority_map.get(conflict_result.severity, "medium"),
                location=(
                    f"Intersection at {conflict_result.intersection_point}"
                    if conflict_result.intersection_point
                    else _("Multiple locations")
                ),
                confidence_score=int(conflict_result.risk_score * 100),
                conflict_3d_geometry=conflict_result.intersection_point,
                conflict_type=conflict_result.conflict_type,
                detected_timestamp=django_timezone.now(),
                detection_method="automated_3d",
                risk_score=int(conflict_result.risk_score * 100),
                is_vertical_conflict=conflict_result.metadata["elevation_data"].get("vertical_conflict", False),
                vertical_clearance_violation=conflict_result.clearance_violation,
            )

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.error(_(f"Error creating conflict model instance: {e}"))
            return None


class ConflictMonitoringService:
    """Service for real-time conflict monitoring and alerting"""

    def __init__(self):
        self.detector = ThreeDConflictDetector()

    def monitor_project_conflicts(self, project, alert_threshold: float = 0.7):
        """Monitor project for new or escalating conflicts"""
        try:
            # Get project utility lines
            from .models import UtilityLineData

            utility_lines = UtilityLineData.objects.filter(project=project)

            # Detect conflicts
            conflicts = self.detector.detect_3d_conflicts(
                utility_lines,
                include_elevation=True,
                risk_threshold=alert_threshold,
            )

            # Process high-risk conflicts
            critical_conflicts = [c for c in conflicts if c.severity == "critical"]

            if critical_conflicts:
                self._send_conflict_alerts(project, critical_conflicts)

            return {
                "total_conflicts": len(conflicts),
                "critical_conflicts": len(critical_conflicts),
                "conflicts": conflicts,
            }

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.error(_(f"Error monitoring project conflicts: {e}"))
            return {"error": str(e)}

    def _send_conflict_alerts(self, project, conflicts: list[ConflictResult]):
        """Send alerts for critical conflicts"""
        try:
            # Import notification system
            from apps.messaging.models import Notification

            for conflict in conflicts:
                # Create notification for project members
                message = _(
                    f"Critical utility conflict detected in {project.name}: "
                    f"{conflict.metadata['line1_type']} and {conflict.metadata['line2_type']} "
                    f"utilities with {conflict.clearance_violation:.2f}m clearance violation",
                )

                # Send to project managers and engineers
                for member in project.members.filter(role__in=["project_manager", "engineer"]):
                    Notification.objects.create(
                        user=member.user,
                        title=_("Critical Utility Conflict"),
                        message=message,
                        category="project",
                        priority="high",
                        project=project,
                        metadata={
                            "conflict_id": conflict.conflict_id,
                            "risk_score": conflict.risk_score,
                            "utility_types": [
                                conflict.metadata["line1_type"],
                                conflict.metadata["line2_type"],
                            ],
                        },
                    )

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.error(_(f"Error sending conflict alerts: {e}"))


class ConflictResolutionEngine:
    """Engine for generating conflict resolution strategies"""

    def __init__(self):
        self.detector = ThreeDConflictDetector()

    def generate_resolution_strategies(self, conflict_result: ConflictResult) -> dict:
        """Generate multiple resolution strategies for a conflict"""
        strategies = []

        try:
            clearance = self.detector._get_clearance_requirements(
                conflict_result.metadata["line1_type"],
                conflict_result.metadata["line2_type"],
            )

            # Strategy 1: Horizontal relocation
            if conflict_result.conflict_type in ["crossing", "proximity"]:
                strategies.append(
                    {
                        "name": _("Horizontal Relocation"),
                        "description": _(
                            f"Relocate one utility to maintain {clearance.horizontal_clearance}m clearance",
                        ),
                        "cost_estimate": _("Medium"),
                        "disruption_level": _("Medium"),
                        "timeline": _("2-4 weeks"),
                        "effectiveness": 0.9,
                    },
                )

            # Strategy 2: Vertical adjustment
            if conflict_result.metadata["elevation_data"].get("has_elevation_data"):
                strategies.append(
                    {
                        "name": _("Depth Adjustment"),
                        "description": _(
                            f"Adjust installation depth to maintain {clearance.vertical_clearance}m vertical clearance",
                        ),
                        "cost_estimate": _("Low"),
                        "disruption_level": _("Low"),
                        "timeline": _("1-2 weeks"),
                        "effectiveness": 0.8,
                    },
                )

            # Strategy 3: Protective measures
            if conflict_result.severity in ["high", "critical"]:
                strategies.append(
                    {
                        "name": _("Protective Installation"),
                        "description": _("Install protective sleeves, barriers, or encasement"),
                        "cost_estimate": _("Low-Medium"),
                        "disruption_level": _("Low"),
                        "timeline": _("1 week"),
                        "effectiveness": 0.7,
                    },
                )

            # Strategy 4: Specialized construction
            if conflict_result.clearance_violation > clearance.horizontal_clearance:
                strategies.append(
                    {
                        "name": _("Specialized Construction"),
                        "description": _("Use directional drilling, jack-and-bore, or other specialized methods"),
                        "cost_estimate": _("High"),
                        "disruption_level": _("Low"),
                        "timeline": _("3-6 weeks"),
                        "effectiveness": 0.95,
                    },
                )

            # Strategy 5: Joint trenching (if compatible utilities)
            utility_pair = tuple(
                sorted(
                    [
                        conflict_result.metadata["line1_type"].lower(),
                        conflict_result.metadata["line2_type"].lower(),
                    ],
                ),
            )

            if utility_pair not in [
                ("gas", "electric"),
                ("gas", "water"),
            ]:  # Not high-risk pairs
                strategies.append(
                    {
                        "name": _("Joint Trenching"),
                        "description": _("Install utilities in shared trench with proper separation"),
                        "cost_estimate": _("Medium"),
                        "disruption_level": _("Medium"),
                        "timeline": _("2-3 weeks"),
                        "effectiveness": 0.85,
                    },
                )

            # Sort strategies by effectiveness
            strategies.sort(key=lambda x: x["effectiveness"], reverse=True)

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.error(_(f"Error generating resolution strategies: {e}"))
            strategies.append(
                {
                    "name": _("Manual Review"),
                    "description": _("Requires manual engineering review"),
                    "cost_estimate": _("Variable"),
                    "disruption_level": _("Variable"),
                    "timeline": _("TBD"),
                    "effectiveness": 0.5,
                },
            )

        return {
            "conflict_id": conflict_result.conflict_id,
            "strategies": strategies,
            "recommended_strategy": strategies[0] if strategies else None,
            "generated_at": django_timezone.now().isoformat(),
        }
