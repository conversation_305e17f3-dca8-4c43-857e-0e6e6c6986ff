"""
Security Event Logging for CLEAR Application

This module provides specialized logging for security-related events
including authentication, authorization, and suspicious activities.
"""

import hashlib
import json
from datetime import datetime, timedelta
from typing import Any, Dict, Optional

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.http import HttpRequest
from django.utils import timezone

from apps.core.logging import get_logger

User = get_user_model()
logger = get_logger('clear.security')


class SecurityEventLogger:
    """
    Centralized security event logging with threat detection and alerting.
    """

    # Security event types
    EVENT_TYPES = {
        'LOGIN_SUCCESS': 'login_success',
        'LOGIN_FAILURE': 'login_failure',
        'LOGOUT': 'logout',
        'PASSWORD_CHANGE': 'password_change',
        'PASSWORD_RESET_REQUEST': 'password_reset_request',
        'PASSWORD_RESET_COMPLETE': 'password_reset_complete',
        'MFA_SETUP': 'mfa_setup',
        'MFA_SUCCESS': 'mfa_success',
        'MFA_FAILURE': 'mfa_failure',
        'PERMISSION_DENIED': 'permission_denied',
        'SUSPICIOUS_ACTIVITY': 'suspicious_activity',
        'ACCOUNT_LOCKED': 'account_locked',
        'ACCOUNT_UNLOCKED': 'account_unlocked',
        'PRIVILEGE_ESCALATION': 'privilege_escalation',
        'DATA_ACCESS': 'data_access',
        'DATA_MODIFICATION': 'data_modification',
        'EXPORT_REQUEST': 'export_request',
        'API_KEY_CREATED': 'api_key_created',
        'API_KEY_REVOKED': 'api_key_revoked',
        'SESSION_HIJACK_ATTEMPT': 'session_hijack_attempt',
        'BRUTE_FORCE_ATTEMPT': 'brute_force_attempt',
    }

    def __init__(self):
        self.alert_threshold = getattr(settings, 'SECURITY_ALERT_THRESHOLD', 5)
        self.alert_window = getattr(settings, 'SECURITY_ALERT_WINDOW', 300)  # 5 minutes
        self.track_ip_addresses = getattr(settings, 'TRACK_IP_ADDRESSES', True)

    def log_authentication_event(self, event_type: str, request: HttpRequest, 
                                user: Optional[User] = None, **kwargs) -> None:
        """Log authentication-related security events."""
        event_data = {
            'event_type': event_type,
            'timestamp': timezone.now().isoformat(),
            'ip_address': self._get_client_ip(request),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'session_key': request.session.session_key,
            'is_secure': request.is_secure(),
            **kwargs
        }

        # Add user information if available
        if user:
            event_data.update({
                'user_id': user.id,
                'user_email': user.email,
                'user_is_staff': user.is_staff,
                'user_is_superuser': user.is_superuser,
            })

            # Add organization context
            if hasattr(user, 'organization') and user.organization:
                event_data['organization_id'] = user.organization.id
                event_data['organization_name'] = user.organization.name

        # Check for suspicious patterns
        self._check_suspicious_patterns(event_type, event_data)

        # Log the event
        if event_type in ['LOGIN_FAILURE', 'MFA_FAILURE', 'PERMISSION_DENIED']:
            logger.warning(
                f"Security event: {event_type}",
                extra={'security_context': event_data}
            )
        elif event_type in ['SUSPICIOUS_ACTIVITY', 'BRUTE_FORCE_ATTEMPT', 'SESSION_HIJACK_ATTEMPT']:
            logger.error(
                f"Security threat detected: {event_type}",
                extra={'security_context': event_data}
            )
        else:
            logger.info(
                f"Security event: {event_type}",
                extra={'security_context': event_data}
            )

    def log_authorization_event(self, event_type: str, request: HttpRequest, 
                              resource: str, action: str, **kwargs) -> None:
        """Log authorization-related security events."""
        event_data = {
            'event_type': event_type,
            'timestamp': timezone.now().isoformat(),
            'resource': resource,
            'action': action,
            'ip_address': self._get_client_ip(request),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'path': request.get_full_path(),
            'method': request.method,
            **kwargs
        }

        # Add user information
        if hasattr(request, 'user') and request.user.is_authenticated:
            event_data.update({
                'user_id': request.user.id,
                'user_email': request.user.email,
                'user_permissions': list(request.user.get_all_permissions()),
            })

        # Log the event
        if event_type == 'PERMISSION_DENIED':
            logger.warning(
                f"Authorization denied: {action} on {resource}",
                extra={'security_context': event_data}
            )
        else:
            logger.info(
                f"Authorization event: {event_type}",
                extra={'security_context': event_data}
            )

    def log_data_access_event(self, request: HttpRequest, resource_type: str, 
                            resource_id: str, action: str, **kwargs) -> None:
        """Log data access events for audit trails."""
        event_data = {
            'event_type': 'DATA_ACCESS',
            'timestamp': timezone.now().isoformat(),
            'resource_type': resource_type,
            'resource_id': resource_id,
            'action': action,
            'ip_address': self._get_client_ip(request),
            'path': request.get_full_path(),
            'method': request.method,
            **kwargs
        }

        # Add user information
        if hasattr(request, 'user') and request.user.is_authenticated:
            event_data.update({
                'user_id': request.user.id,
                'user_email': request.user.email,
            })

            # Add organization context
            if hasattr(request.user, 'organization') and request.user.organization:
                event_data['organization_id'] = request.user.organization.id

        logger.info(
            f"Data access: {action} on {resource_type}:{resource_id}",
            extra={'security_context': event_data}
        )

    def log_suspicious_activity(self, request: HttpRequest, activity_type: str, 
                              details: Dict[str, Any]) -> None:
        """Log suspicious activities that may indicate security threats."""
        event_data = {
            'event_type': 'SUSPICIOUS_ACTIVITY',
            'activity_type': activity_type,
            'timestamp': timezone.now().isoformat(),
            'ip_address': self._get_client_ip(request),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'path': request.get_full_path(),
            'method': request.method,
            'details': details,
        }

        # Add user information if available
        if hasattr(request, 'user') and request.user.is_authenticated:
            event_data.update({
                'user_id': request.user.id,
                'user_email': request.user.email,
            })

        logger.error(
            f"Suspicious activity detected: {activity_type}",
            extra={'security_context': event_data}
        )

        # Trigger additional security measures if needed
        self._handle_suspicious_activity(request, activity_type, details)

    def _check_suspicious_patterns(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """Check for suspicious patterns in security events."""
        if not self.track_ip_addresses:
            return

        ip_address = event_data.get('ip_address')
        if not ip_address:
            return

        # Create cache key for IP tracking
        cache_key = f"security_events:{ip_address}:{event_type}"
        
        # Get current count
        current_count = cache.get(cache_key, 0)
        current_count += 1
        
        # Update cache
        cache.set(cache_key, current_count, self.alert_window)

        # Check if threshold exceeded
        if current_count >= self.alert_threshold:
            if event_type == 'LOGIN_FAILURE':
                self._handle_brute_force_attempt(ip_address, current_count)
            elif event_type == 'PERMISSION_DENIED':
                self._handle_privilege_escalation_attempt(ip_address, current_count)

    def _handle_brute_force_attempt(self, ip_address: str, attempt_count: int) -> None:
        """Handle detected brute force attempts."""
        logger.error(
            f"Brute force attack detected from {ip_address}",
            extra={
                'security_context': {
                    'event_type': 'BRUTE_FORCE_ATTEMPT',
                    'ip_address': ip_address,
                    'attempt_count': attempt_count,
                    'timestamp': timezone.now().isoformat(),
                }
            }
        )

        # Implement IP blocking or rate limiting here
        # This could integrate with fail2ban, CloudFlare, or similar services

    def _handle_privilege_escalation_attempt(self, ip_address: str, attempt_count: int) -> None:
        """Handle detected privilege escalation attempts."""
        logger.error(
            f"Privilege escalation attempt detected from {ip_address}",
            extra={
                'security_context': {
                    'event_type': 'PRIVILEGE_ESCALATION',
                    'ip_address': ip_address,
                    'attempt_count': attempt_count,
                    'timestamp': timezone.now().isoformat(),
                }
            }
        )

    def _handle_suspicious_activity(self, request: HttpRequest, activity_type: str, 
                                  details: Dict[str, Any]) -> None:
        """Handle suspicious activities with appropriate responses."""
        # This could trigger additional security measures like:
        # - Temporary account suspension
        # - Additional MFA requirements
        # - Admin notifications
        # - IP blocking
        pass

    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or 'unknown'


# Global security logger instance
security_logger = SecurityEventLogger()


# Convenience functions for common security events
def log_login_success(request: HttpRequest, user: User) -> None:
    """Log successful login."""
    security_logger.log_authentication_event('LOGIN_SUCCESS', request, user)


def log_login_failure(request: HttpRequest, username: str = None, reason: str = None) -> None:
    """Log failed login attempt."""
    security_logger.log_authentication_event(
        'LOGIN_FAILURE', 
        request, 
        username=username, 
        failure_reason=reason
    )


def log_logout(request: HttpRequest, user: User) -> None:
    """Log user logout."""
    security_logger.log_authentication_event('LOGOUT', request, user)


def log_permission_denied(request: HttpRequest, resource: str, action: str) -> None:
    """Log permission denied event."""
    security_logger.log_authorization_event('PERMISSION_DENIED', request, resource, action)


def log_data_access(request: HttpRequest, resource_type: str, resource_id: str, 
                   action: str = 'read') -> None:
    """Log data access event."""
    security_logger.log_data_access_event(request, resource_type, resource_id, action)


def log_suspicious_activity(request: HttpRequest, activity_type: str, 
                          details: Dict[str, Any]) -> None:
    """Log suspicious activity."""
    security_logger.log_suspicious_activity(request, activity_type, details)
