"""
Business Logic Logging for CLEAR Application

This module provides specialized logging for business events, audit trails,
and operational metrics to support analytics and compliance requirements.
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from django.contrib.auth import get_user_model
from django.http import HttpRequest
from django.utils import timezone

from apps.core.logging import get_logger

User = get_user_model()
logger = get_logger('clear.business')


class BusinessEventLogger:
    """
    Centralized business event logging for audit trails and analytics.
    """

    # Business event categories
    CATEGORIES = {
        'PROJECT_MANAGEMENT': 'project_management',
        'INFRASTRUCTURE': 'infrastructure',
        'DOCUMENT_MANAGEMENT': 'document_management',
        'USER_MANAGEMENT': 'user_management',
        'ORGANIZATION_MANAGEMENT': 'organization_management',
        'FINANCIAL': 'financial',
        'COMPLIANCE': 'compliance',
        'ANALYTICS': 'analytics',
        'SYSTEM': 'system',
    }

    # Event types by category
    EVENT_TYPES = {
        'PROJECT_MANAGEMENT': [
            'project_created', 'project_updated', 'project_deleted',
            'project_status_changed', 'project_assigned', 'project_completed',
            'task_created', 'task_updated', 'task_completed', 'task_assigned',
            'milestone_reached', 'deadline_missed', 'timeline_updated'
        ],
        'INFRASTRUCTURE': [
            'utility_line_created', 'utility_line_updated', 'utility_line_deleted',
            'conflict_detected', 'conflict_resolved', 'spatial_analysis_performed',
            'map_data_imported', 'coordinates_updated', 'buffer_analysis_completed'
        ],
        'DOCUMENT_MANAGEMENT': [
            'document_uploaded', 'document_downloaded', 'document_shared',
            'document_version_created', 'document_approved', 'document_rejected',
            'document_archived', 'document_restored', 'document_permissions_changed'
        ],
        'USER_MANAGEMENT': [
            'user_created', 'user_updated', 'user_deactivated', 'user_reactivated',
            'role_assigned', 'role_revoked', 'permissions_changed',
            'profile_updated', 'preferences_changed'
        ],
        'ORGANIZATION_MANAGEMENT': [
            'organization_created', 'organization_updated', 'organization_settings_changed',
            'member_invited', 'member_joined', 'member_removed',
            'subscription_changed', 'billing_updated'
        ],
        'FINANCIAL': [
            'invoice_created', 'invoice_sent', 'payment_received',
            'expense_recorded', 'budget_updated', 'cost_analysis_performed',
            'time_entry_created', 'time_entry_approved'
        ],
        'COMPLIANCE': [
            'compliance_check_performed', 'regulation_updated',
            'audit_trail_generated', 'report_submitted',
            'violation_detected', 'remediation_completed'
        ],
    }

    def log_business_event(self, category: str, event_type: str, 
                          request: HttpRequest = None, **kwargs) -> None:
        """
        Log a business event with comprehensive context.
        
        Args:
            category: Business category (e.g., 'PROJECT_MANAGEMENT')
            event_type: Specific event type (e.g., 'project_created')
            request: HTTP request object for context
            **kwargs: Additional event data
        """
        event_data = {
            'category': category,
            'event_type': event_type,
            'timestamp': timezone.now().isoformat(),
            **kwargs
        }

        # Add request context if available
        if request:
            event_data.update({
                'ip_address': self._get_client_ip(request),
                'user_agent': request.headers.get('User-Agent'),
                'path': request.get_full_path(),
                'method': request.method,
            })

            # Add user context
            if hasattr(request, 'user') and request.user.is_authenticated:
                event_data.update({
                    'user_id': request.user.id,
                    'user_email': request.user.email,
                })

                # Add organization context
                if hasattr(request.user, 'organization') and request.user.organization:
                    event_data.update({
                        'organization_id': request.user.organization.id,
                        'organization_name': request.user.organization.name,
                    })

        # Log the business event
        logger.info(
            f"Business event: {category}.{event_type}",
            extra={'business_context': event_data}
        )

    def log_project_event(self, event_type: str, project_id: str, 
                         request: HttpRequest = None, **kwargs) -> None:
        """Log project-related business events."""
        self.log_business_event(
            'PROJECT_MANAGEMENT',
            event_type,
            request,
            project_id=project_id,
            **kwargs
        )

    def log_infrastructure_event(self, event_type: str, request: HttpRequest = None, 
                               **kwargs) -> None:
        """Log infrastructure-related business events."""
        self.log_business_event(
            'INFRASTRUCTURE',
            event_type,
            request,
            **kwargs
        )

    def log_document_event(self, event_type: str, document_id: str, 
                          request: HttpRequest = None, **kwargs) -> None:
        """Log document-related business events."""
        self.log_business_event(
            'DOCUMENT_MANAGEMENT',
            event_type,
            request,
            document_id=document_id,
            **kwargs
        )

    def log_user_event(self, event_type: str, target_user_id: str = None,
                      request: HttpRequest = None, **kwargs) -> None:
        """Log user management events."""
        self.log_business_event(
            'USER_MANAGEMENT',
            event_type,
            request,
            target_user_id=target_user_id,
            **kwargs
        )

    def log_financial_event(self, event_type: str, amount: float = None,
                           currency: str = 'USD', request: HttpRequest = None, 
                           **kwargs) -> None:
        """Log financial events."""
        self.log_business_event(
            'FINANCIAL',
            event_type,
            request,
            amount=amount,
            currency=currency,
            **kwargs
        )

    def log_compliance_event(self, event_type: str, regulation_type: str = None,
                           request: HttpRequest = None, **kwargs) -> None:
        """Log compliance-related events."""
        self.log_business_event(
            'COMPLIANCE',
            event_type,
            request,
            regulation_type=regulation_type,
            **kwargs
        )

    def log_workflow_event(self, workflow_name: str, step: str, status: str,
                          request: HttpRequest = None, **kwargs) -> None:
        """Log workflow progression events."""
        self.log_business_event(
            'SYSTEM',
            'workflow_step_completed',
            request,
            workflow_name=workflow_name,
            workflow_step=step,
            workflow_status=status,
            **kwargs
        )

    def log_performance_metric(self, metric_name: str, value: Union[int, float],
                             unit: str = None, tags: Dict[str, str] = None,
                             request: HttpRequest = None) -> None:
        """Log performance metrics for monitoring."""
        self.log_business_event(
            'ANALYTICS',
            'performance_metric_recorded',
            request,
            metric_name=metric_name,
            metric_value=value,
            metric_unit=unit,
            metric_tags=tags or {},
        )

    def log_data_export(self, export_type: str, record_count: int,
                       file_format: str, request: HttpRequest = None, 
                       **kwargs) -> None:
        """Log data export events for compliance."""
        self.log_business_event(
            'COMPLIANCE',
            'data_exported',
            request,
            export_type=export_type,
            record_count=record_count,
            file_format=file_format,
            **kwargs
        )

    def log_integration_event(self, integration_name: str, event_type: str,
                            success: bool, request: HttpRequest = None, 
                            **kwargs) -> None:
        """Log external integration events."""
        self.log_business_event(
            'SYSTEM',
            'integration_event',
            request,
            integration_name=integration_name,
            integration_event_type=event_type,
            integration_success=success,
            **kwargs
        )

    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or 'unknown'


class AuditTrailLogger:
    """
    Specialized logger for audit trails and compliance reporting.
    """

    def __init__(self):
        self.audit_logger = get_logger('clear.audit')

    def log_data_change(self, model_name: str, instance_id: str, action: str,
                       changes: Dict[str, Any], request: HttpRequest = None,
                       **kwargs) -> None:
        """Log data changes for audit trails."""
        audit_data = {
            'audit_type': 'data_change',
            'model_name': model_name,
            'instance_id': instance_id,
            'action': action,  # 'create', 'update', 'delete'
            'changes': changes,
            'timestamp': timezone.now().isoformat(),
            **kwargs
        }

        # Add user context
        if request and hasattr(request, 'user') and request.user.is_authenticated:
            audit_data.update({
                'user_id': request.user.id,
                'user_email': request.user.email,
                'ip_address': self._get_client_ip(request),
            })

        self.audit_logger.info(
            f"Data change: {action} {model_name}:{instance_id}",
            extra={'audit_context': audit_data}
        )

    def log_access_event(self, resource_type: str, resource_id: str,
                        action: str, request: HttpRequest = None, **kwargs) -> None:
        """Log resource access for audit trails."""
        audit_data = {
            'audit_type': 'access_event',
            'resource_type': resource_type,
            'resource_id': resource_id,
            'action': action,
            'timestamp': timezone.now().isoformat(),
            **kwargs
        }

        # Add user context
        if request and hasattr(request, 'user') and request.user.is_authenticated:
            audit_data.update({
                'user_id': request.user.id,
                'user_email': request.user.email,
                'ip_address': self._get_client_ip(request),
            })

        self.audit_logger.info(
            f"Access event: {action} {resource_type}:{resource_id}",
            extra={'audit_context': audit_data}
        )

    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or 'unknown'


# Global logger instances
business_logger = BusinessEventLogger()
audit_logger = AuditTrailLogger()


# Convenience functions for common business events
def log_project_created(project_id: str, request: HttpRequest = None, **kwargs) -> None:
    """Log project creation event."""
    business_logger.log_project_event('project_created', project_id, request, **kwargs)


def log_document_uploaded(document_id: str, request: HttpRequest = None, **kwargs) -> None:
    """Log document upload event."""
    business_logger.log_document_event('document_uploaded', document_id, request, **kwargs)


def log_conflict_detected(conflict_data: Dict[str, Any], request: HttpRequest = None) -> None:
    """Log infrastructure conflict detection."""
    business_logger.log_infrastructure_event('conflict_detected', request, **conflict_data)


def log_user_action(action: str, target_user_id: str = None, 
                   request: HttpRequest = None, **kwargs) -> None:
    """Log user management action."""
    business_logger.log_user_event(action, target_user_id, request, **kwargs)


def log_data_change(model_name: str, instance_id: str, action: str,
                   changes: Dict[str, Any], request: HttpRequest = None) -> None:
    """Log data change for audit trail."""
    audit_logger.log_data_change(model_name, instance_id, action, changes, request)
