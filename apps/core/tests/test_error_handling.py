"""
Tests for CLEAR Error Handling System

This module contains comprehensive tests for the error handling infrastructure,
including exceptions, middleware, logging, and decorators.
"""

import json
import logging
from unittest.mock import Mock, patch

import pytest
from django.contrib.auth import get_user_model
from django.core.exceptions import PermissionDenied, ValidationError as DjangoValidationError
from django.http import HttpRequest, HttpResponse
from django.test import RequestFactory, TestCase, override_settings

from apps.core.exceptions import (
    ClearBaseException,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    ResourceNotFoundError,
    BusinessRuleError,
    convert_django_exception,
    handle_exception_chain,
)
from apps.core.logging import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>atter, get_logger
from apps.core.middleware.error_handling_middleware import ClearErrorHandlingMiddleware
from apps.core.decorators.error_handling import (
    handle_exceptions,
    require_organization_access,
    validate_request_data,
    ErrorHandlingMixin,
)

User = get_user_model()


class TestClearExceptions(TestCase):
    """Test CLEAR exception hierarchy."""

    def test_base_exception_creation(self):
        """Test ClearBaseException creation and properties."""
        exc = ClearBaseException(
            "Test error",
            error_code="TEST_ERROR",
            details={'key': 'value'},
            user_message="User friendly message",
            severity="high"
        )
        
        self.assertEqual(exc.message, "Test error")
        self.assertEqual(exc.error_code, "TEST_ERROR")
        self.assertEqual(exc.details, {'key': 'value'})
        self.assertEqual(exc.user_message, "User friendly message")
        self.assertEqual(exc.severity, "high")
        self.assertIsNotNone(exc.error_id)
        self.assertIsNotNone(exc.timestamp)

    def test_exception_to_dict(self):
        """Test exception serialization to dictionary."""
        exc = ValidationError("Invalid data", details={'field': 'error'})
        result = exc.to_dict()
        
        self.assertIn('error_id', result)
        self.assertIn('error_code', result)
        self.assertIn('message', result)
        self.assertIn('user_message', result)
        self.assertIn('severity', result)
        self.assertIn('category', result)
        self.assertIn('details', result)
        self.assertIn('timestamp', result)

    def test_exception_to_json_response(self):
        """Test exception serialization for JSON responses."""
        exc = AuthenticationError("Login required")
        result = exc.to_json_response()
        
        self.assertTrue(result['error'])
        self.assertIn('error_id', result)
        self.assertIn('error_code', result)
        self.assertIn('message', result)
        self.assertIn('timestamp', result)

    def test_validation_error_defaults(self):
        """Test ValidationError default values."""
        exc = ValidationError("Invalid input")
        
        self.assertEqual(exc.severity, "medium")
        self.assertEqual(exc.category, "validation")
        self.assertEqual(exc.error_code, "VALIDATION_ERROR")

    def test_business_rule_error_defaults(self):
        """Test BusinessRuleError default values."""
        exc = BusinessRuleError("Rule violation")
        
        self.assertEqual(exc.severity, "high")
        self.assertEqual(exc.category, "business_logic")
        self.assertEqual(exc.error_code, "BUSINESS_RULE_ERROR")

    def test_convert_django_exception(self):
        """Test conversion of Django exceptions to CLEAR exceptions."""
        django_exc = DjangoValidationError("Django validation error")
        clear_exc = convert_django_exception(django_exc)
        
        self.assertIsInstance(clear_exc, ValidationError)
        self.assertIn("Django validation error", clear_exc.message)

    def test_handle_exception_chain(self):
        """Test handling of exception chains."""
        try:
            try:
                raise ValueError("Original error")
            except ValueError as e:
                raise KeyError("Chained error") from e
        except Exception as e:
            clear_exc = handle_exception_chain(e)
            
            self.assertIsInstance(clear_exc, ClearBaseException)
            self.assertIn('exception_chain', clear_exc.details)


class TestJSONFormatter(TestCase):
    """Test JSON logging formatter."""

    def setUp(self):
        self.formatter = JSONFormatter()

    def test_basic_formatting(self):
        """Test basic log record formatting."""
        record = logging.LogRecord(
            name='test.logger',
            level=logging.ERROR,
            pathname='',
            lineno=0,
            msg='Test message',
            args=(),
            exc_info=None
        )
        
        result = self.formatter.format(record)
        data = json.loads(result)
        
        self.assertIn('timestamp', data)
        self.assertIn('level', data)
        self.assertIn('logger', data)
        self.assertIn('message', data)
        self.assertIn('service', data)
        self.assertIn('environment', data)
        self.assertEqual(data['level'], 'ERROR')
        self.assertEqual(data['message'], 'Test message')

    def test_exception_formatting(self):
        """Test formatting with exception information."""
        try:
            raise ValueError("Test exception")
        except ValueError:
            record = logging.LogRecord(
                name='test.logger',
                level=logging.ERROR,
                pathname='',
                lineno=0,
                msg='Error occurred',
                args=(),
                exc_info=True
            )
            
            result = self.formatter.format(record)
            data = json.loads(result)
            
            self.assertIn('exception', data)
            self.assertEqual(data['exception']['type'], 'ValueError')
            self.assertEqual(data['exception']['message'], 'Test exception')
            self.assertIn('traceback', data['exception'])

    def test_extra_context_formatting(self):
        """Test formatting with extra context."""
        record = logging.LogRecord(
            name='test.logger',
            level=logging.INFO,
            pathname='',
            lineno=0,
            msg='Test message',
            args=(),
            exc_info=None
        )
        record.error_context = {'error_id': '12345'}
        record.request_context = {'method': 'GET'}
        
        result = self.formatter.format(record)
        data = json.loads(result)
        
        self.assertIn('error_context', data)
        self.assertIn('request_context', data)
        self.assertEqual(data['error_context']['error_id'], '12345')
        self.assertEqual(data['request_context']['method'], 'GET')


class TestClearLogger(TestCase):
    """Test CLEAR logger functionality."""

    def setUp(self):
        self.logger = ClearLogger('test.logger')

    @patch('apps.core.logging.logging.getLogger')
    def test_logger_creation(self, mock_get_logger):
        """Test logger creation."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        logger = ClearLogger('test')
        self.assertEqual(logger.logger, mock_logger)

    def test_correlation_id_setting(self):
        """Test correlation ID setting."""
        self.logger.set_correlation_id('test-correlation-id')
        self.assertEqual(self.logger.correlation_id, 'test-correlation-id')

    @patch('apps.core.logging.logging.getLogger')
    def test_log_request(self, mock_get_logger):
        """Test request logging."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        factory = RequestFactory()
        request = factory.get('/test/')
        request.user = Mock()
        request.user.is_authenticated = True
        request.user.id = 1
        request.user.email = '<EMAIL>'
        request.user.is_staff = False
        
        logger = ClearLogger('test')
        logger.log_request(request, response_status=200, duration=0.5)
        
        mock_logger.info.assert_called_once()
        call_args = mock_logger.info.call_args
        self.assertIn('GET /test/', call_args[0][0])
        self.assertIn('request_context', call_args[1]['extra'])
        self.assertIn('user_context', call_args[1]['extra'])

    @patch('apps.core.logging.logging.getLogger')
    def test_log_exception(self, mock_get_logger):
        """Test exception logging."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        logger = ClearLogger('test')
        exc = ValidationError("Test validation error")
        
        logger.log_exception(exc)
        
        mock_logger.error.assert_called_once()
        call_args = mock_logger.error.call_args
        self.assertIn('Exception occurred', call_args[0][0])
        self.assertIn('error_context', call_args[1]['extra'])


@pytest.mark.django_db
class TestErrorHandlingMiddleware(TestCase):
    """Test error handling middleware."""

    def setUp(self):
        self.factory = RequestFactory()
        self.middleware = ClearErrorHandlingMiddleware(lambda r: HttpResponse())

    def test_htmx_request_detection(self):
        """Test HTMX request detection."""
        request = self.factory.get('/', HTTP_HX_REQUEST='true')
        self.assertTrue(self.middleware._is_htmx_request(request))
        
        request = self.factory.get('/', HTTP_X_REQUESTED_WITH='XMLHttpRequest')
        self.assertTrue(self.middleware._is_htmx_request(request))
        
        request = self.factory.get('/')
        self.assertFalse(self.middleware._is_htmx_request(request))

    def test_api_request_detection(self):
        """Test API request detection."""
        request = self.factory.get('/api/test/')
        self.assertTrue(self.middleware._is_api_request(request))
        
        request = self.factory.post('/', content_type='application/json')
        self.assertTrue(self.middleware._is_api_request(request))
        
        request = self.factory.get('/', HTTP_ACCEPT='application/json')
        self.assertTrue(self.middleware._is_api_request(request))

    def test_clear_exception_handling(self):
        """Test handling of CLEAR exceptions."""
        request = self.factory.get('/')
        exc = ValidationError("Test validation error")
        
        # Should not return None (let middleware handle it)
        result = self.middleware.process_exception(request, exc)
        self.assertIsNone(result)  # Middleware should let it propagate

    def test_django_exception_conversion(self):
        """Test conversion of Django exceptions."""
        request = self.factory.get('/')
        exc = DjangoValidationError("Django validation error")
        
        result = self.middleware.process_exception(request, exc)
        self.assertIsNone(result)  # Should convert and re-raise


class TestErrorHandlingDecorators(TestCase):
    """Test error handling decorators."""

    def setUp(self):
        self.factory = RequestFactory()

    def test_handle_exceptions_decorator(self):
        """Test handle_exceptions decorator."""
        @handle_exceptions({ValueError: ValidationError})
        def test_view(request):
            raise ValueError("Test error")
        
        request = self.factory.get('/')
        
        with self.assertRaises(ValidationError):
            test_view(request)

    def test_require_organization_access_decorator(self):
        """Test require_organization_access decorator."""
        @require_organization_access
        def test_view(request):
            return HttpResponse("Success")
        
        # Test unauthenticated user
        request = self.factory.get('/')
        request.user = Mock()
        request.user.is_authenticated = False
        
        with self.assertRaises(AuthenticationError):
            test_view(request)
        
        # Test user without organization
        request.user.is_authenticated = True
        request.user.organization = None
        
        with self.assertRaises(AuthorizationError):
            test_view(request)

    def test_validate_request_data_decorator(self):
        """Test validate_request_data decorator."""
        @validate_request_data(
            required_fields=['name'],
            validation_rules={'email': lambda x: '@' in x}
        )
        def test_view(request):
            return HttpResponse("Success")
        
        # Test missing required field
        request = self.factory.post('/', {})
        
        with self.assertRaises(ValidationError) as cm:
            test_view(request)
        
        self.assertIn('field_errors', cm.exception.details)
        
        # Test invalid email
        request = self.factory.post('/', {'name': 'Test', 'email': 'invalid'})
        
        with self.assertRaises(ValidationError):
            test_view(request)
        
        # Test valid data
        request = self.factory.post('/', {'name': 'Test', 'email': '<EMAIL>'})
        response = test_view(request)
        self.assertEqual(response.status_code, 200)


class TestErrorHandlingMixin(TestCase):
    """Test ErrorHandlingMixin for class-based views."""

    def setUp(self):
        self.factory = RequestFactory()

    def test_mixin_exception_handling(self):
        """Test exception handling in mixin."""
        from django.views import View
        
        class TestView(ErrorHandlingMixin, View):
            exception_mapping = {ValueError: ValidationError}
            
            def get(self, request):
                raise ValueError("Test error")
        
        view = TestView()
        request = self.factory.get('/')
        
        with self.assertRaises(ValidationError):
            view.dispatch(request)
