"""
Structured Logging Utilities for CLEAR Application

This module provides structured logging capabilities with JSON formatting,
context enrichment, and integration with the CLEAR exception hierarchy.
"""

import json
import logging
import sys
import traceback
from datetime import datetime
from typing import Any, Dict, Optional, Union
from uuid import uuid4

from django.conf import settings
from django.http import HttpRequest
from django.utils import timezone

from apps.core.exceptions import ClearBaseException


class JSONFormatter(logging.Formatter):
    """
    JSON formatter for structured logging.
    
    Formats log records as JSON with consistent structure and metadata.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.hostname = getattr(settings, 'HOSTNAME', 'unknown')
        self.service_name = getattr(settings, 'SERVICE_NAME', 'clear')
        self.environment = getattr(settings, 'ENVIRONMENT', 'development')

    def format(self, record: logging.LogRecord) -> str:
        """Format log record as <PERSON><PERSON><PERSON>."""
        # Base log structure
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'service': self.service_name,
            'environment': self.environment,
            'hostname': self.hostname,
        }

        # Add exception information if present
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': self.formatException(record.exc_info),
            }

        # Add extra context if present
        if hasattr(record, 'error_context'):
            log_entry['error_context'] = record.error_context

        if hasattr(record, 'request_context'):
            log_entry['request_context'] = record.request_context

        if hasattr(record, 'user_context'):
            log_entry['user_context'] = record.user_context

        if hasattr(record, 'business_context'):
            log_entry['business_context'] = record.business_context

        # Add any other extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info', 'error_context', 'request_context',
                          'user_context', 'business_context']:
                if not key.startswith('_'):
                    log_entry[key] = value

        return json.dumps(log_entry, default=str, ensure_ascii=False)


class ClearLogger:
    """
    Enhanced logger for CLEAR application with structured logging capabilities.
    """

    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.correlation_id = None

    def set_correlation_id(self, correlation_id: str) -> None:
        """Set correlation ID for request tracking."""
        self.correlation_id = correlation_id

    def _add_context(self, extra: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Add common context to log entries."""
        if self.correlation_id:
            extra['correlation_id'] = self.correlation_id

        # Add any additional context
        for key, value in kwargs.items():
            extra[key] = value

        return extra

    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs) -> None:
        """Log debug message with context."""
        extra = extra or {}
        self.logger.debug(message, extra=self._add_context(extra, **kwargs))

    def info(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs) -> None:
        """Log info message with context."""
        extra = extra or {}
        self.logger.info(message, extra=self._add_context(extra, **kwargs))

    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs) -> None:
        """Log warning message with context."""
        extra = extra or {}
        self.logger.warning(message, extra=self._add_context(extra, **kwargs))

    def error(self, message: str, extra: Optional[Dict[str, Any]] = None, 
              exc_info: bool = False, **kwargs) -> None:
        """Log error message with context."""
        extra = extra or {}
        self.logger.error(message, extra=self._add_context(extra, **kwargs), exc_info=exc_info)

    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None, 
                 exc_info: bool = False, **kwargs) -> None:
        """Log critical message with context."""
        extra = extra or {}
        self.logger.critical(message, extra=self._add_context(extra, **kwargs), exc_info=exc_info)

    def log_request(self, request: HttpRequest, response_status: int = None, 
                   duration: float = None) -> None:
        """Log HTTP request with context."""
        request_context = {
            'method': request.method,
            'path': request.get_full_path(),
            'user_agent': request.headers.get('User-Agent'),
            'ip_address': self._get_client_ip(request),
            'referer': request.headers.get('Referer'),
            'content_type': request.content_type,
            'is_htmx': request.headers.get('HX-Request') == 'true',
            'is_secure': request.is_secure(),
        }

        if response_status:
            request_context['response_status'] = response_status

        if duration:
            request_context['duration_ms'] = round(duration * 1000, 2)

        # Add user context if available
        user_context = {}
        if hasattr(request, 'user') and request.user.is_authenticated:
            user_context = {
                'user_id': request.user.id,
                'user_email': request.user.email,
                'is_staff': request.user.is_staff,
            }

            # Add organization context
            if hasattr(request.user, 'organization') and request.user.organization:
                user_context['organization_id'] = request.user.organization.id
                user_context['organization_name'] = request.user.organization.name

        self.info(
            f"{request.method} {request.get_full_path()}",
            extra={
                'request_context': request_context,
                'user_context': user_context,
            }
        )

    def log_exception(self, exception: Exception, request: HttpRequest = None, 
                     context: Dict[str, Any] = None) -> None:
        """Log exception with comprehensive context."""
        error_context = {
            'exception_type': exception.__class__.__name__,
            'exception_message': str(exception),
            'traceback': traceback.format_exc(),
        }

        # Add CLEAR exception context if available
        if isinstance(exception, ClearBaseException):
            error_context.update({
                'error_id': exception.error_id,
                'error_code': exception.error_code,
                'severity': exception.severity,
                'category': exception.category,
                'user_message': exception.user_message,
                'details': exception.details,
            })

        # Add request context if available
        request_context = {}
        if request:
            request_context = {
                'method': request.method,
                'path': request.get_full_path(),
                'user_agent': request.headers.get('User-Agent'),
                'ip_address': self._get_client_ip(request),
            }

        # Add additional context
        if context:
            error_context['additional_context'] = context

        self.error(
            f"Exception occurred: {exception.__class__.__name__}: {exception}",
            extra={
                'error_context': error_context,
                'request_context': request_context,
            },
            exc_info=True
        )

    def log_business_event(self, event_type: str, event_data: Dict[str, Any], 
                          request: HttpRequest = None) -> None:
        """Log business events for analytics and auditing."""
        business_context = {
            'event_type': event_type,
            'event_data': event_data,
            'timestamp': timezone.now().isoformat(),
        }

        # Add user context if available
        user_context = {}
        if request and hasattr(request, 'user') and request.user.is_authenticated:
            user_context = {
                'user_id': request.user.id,
                'user_email': request.user.email,
            }

            if hasattr(request.user, 'organization') and request.user.organization:
                user_context['organization_id'] = request.user.organization.id

        self.info(
            f"Business event: {event_type}",
            extra={
                'business_context': business_context,
                'user_context': user_context,
            }
        )

    def log_performance(self, operation: str, duration: float, 
                       metadata: Dict[str, Any] = None) -> None:
        """Log performance metrics."""
        performance_context = {
            'operation': operation,
            'duration_ms': round(duration * 1000, 2),
            'metadata': metadata or {},
        }

        level = 'warning' if duration > 1.0 else 'info'
        getattr(self, level)(
            f"Performance: {operation} took {duration:.3f}s",
            extra={'performance_context': performance_context}
        )

    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or 'unknown'


# Global logger instances
def get_logger(name: str) -> ClearLogger:
    """Get a CLEAR logger instance."""
    return ClearLogger(name)


# Convenience functions
def log_request(request: HttpRequest, response_status: int = None, 
               duration: float = None) -> None:
    """Log HTTP request."""
    logger = get_logger('clear.requests')
    logger.log_request(request, response_status, duration)


def log_exception(exception: Exception, request: HttpRequest = None, 
                 context: Dict[str, Any] = None) -> None:
    """Log exception."""
    logger = get_logger('clear.exceptions')
    logger.log_exception(exception, request, context)


def log_business_event(event_type: str, event_data: Dict[str, Any], 
                      request: HttpRequest = None) -> None:
    """Log business event."""
    logger = get_logger('clear.business')
    logger.log_business_event(event_type, event_data, request)


def log_performance(operation: str, duration: float, 
                   metadata: Dict[str, Any] = None) -> None:
    """Log performance metrics."""
    logger = get_logger('clear.performance')
    logger.log_performance(operation, duration, metadata)
