"""
Comprehensive Error Handling Middleware for CLEAR Application

This middleware provides standardized error handling across all requests,
integrating with the CLEAR exception hierarchy and providing consistent
error responses for both HTMX and standard requests.
"""

import json
import logging
import traceback
from typing import Any, Dict, Optional, Union

from django.conf import settings
from django.core.exceptions import PermissionDenied, ValidationError as DjangoValidationError
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import render
from django.utils.deprecation import MiddlewareMixin
from django.utils.translation import gettext as _

from apps.core.exceptions import (
    ClearBaseException,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    ResourceNotFoundError,
    convert_django_exception,
    handle_exception_chain,
)

logger = logging.getLogger(__name__)


class ClearErrorHandlingMiddleware(MiddlewareMixin):
    """
    Comprehensive error handling middleware for CLEAR application.
    
    Features:
    - Integrates with CLEAR exception hierarchy
    - Provides consistent error responses for HTMX and standard requests
    - Structured error logging with context
    - Error response templates
    - Development vs production error details
    - Error tracking and analytics
    """

    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.debug = getattr(settings, 'DEBUG', False)
        self.log_errors = getattr(settings, 'CLEAR_LOG_ERRORS', True)
        self.include_traceback = getattr(settings, 'CLEAR_INCLUDE_TRACEBACK', self.debug)
        self.error_templates = {
            'default': 'errors/error.html',
            'htmx': 'errors/htmx_error.html',
            '404': 'errors/404.html',
            '403': 'errors/403.html',
            '500': 'errors/500.html',
        }

    def process_exception(self, request: HttpRequest, exception: Exception) -> Optional[HttpResponse]:
        """
        Process exceptions with comprehensive error handling.

        Args:
            request: The HTTP request object
            exception: The exception that was raised

        Returns:
            Appropriate error response or None to continue processing
        """
        # Convert to CLEAR exception if needed
        if not isinstance(exception, ClearBaseException):
            if isinstance(exception, (DjangoValidationError, PermissionDenied)):
                clear_exception = convert_django_exception(exception)
            else:
                clear_exception = handle_exception_chain(exception)
        else:
            clear_exception = exception

        # Log the error with context
        if self.log_errors:
            self._log_error_with_context(request, clear_exception)

        # Create appropriate response
        if self._is_htmx_request(request):
            return self._create_htmx_error_response(request, clear_exception)
        elif self._is_api_request(request):
            return self._create_api_error_response(request, clear_exception)
        else:
            return self._create_html_error_response(request, clear_exception)

    def _is_htmx_request(self, request: HttpRequest) -> bool:
        """Check if the request is an HTMX request."""
        return (
            request.headers.get('HX-Request') == 'true' or
            request.headers.get('X-Requested-With') == 'XMLHttpRequest'
        )

    def _is_api_request(self, request: HttpRequest) -> bool:
        """Check if the request is an API request."""
        return (
            request.path.startswith('/api/') or
            request.content_type == 'application/json' or
            'application/json' in request.headers.get('Accept', '')
        )

    def _log_error_with_context(self, request: HttpRequest, exception: ClearBaseException) -> None:
        """Log error with comprehensive context information."""
        try:
            # Build context information
            context = {
                'error_id': exception.error_id,
                'error_code': exception.error_code,
                'severity': exception.severity,
                'category': exception.category,
                'user_message': exception.user_message,
                'details': exception.details,
                'request_info': {
                    'method': request.method,
                    'path': request.get_full_path(),
                    'user_agent': request.headers.get('User-Agent', 'Unknown'),
                    'ip_address': self._get_client_ip(request),
                    'referer': request.headers.get('Referer'),
                    'is_htmx': self._is_htmx_request(request),
                    'is_api': self._is_api_request(request),
                },
                'user_info': self._get_user_context(request),
                'organization_info': self._get_organization_context(request),
                'timestamp': exception.timestamp.isoformat(),
            }

            # Add traceback in debug mode
            if self.include_traceback:
                context['traceback'] = traceback.format_exc()

            # Log with appropriate level
            log_level = getattr(exception, 'log_level', logging.ERROR)
            logger.log(
                log_level,
                f"CLEAR Error [{exception.error_code}]: {exception.message}",
                extra={'error_context': context}
            )

        except Exception as log_error:
            # Fallback logging if context extraction fails
            logger.error(
                f"Error logging failed for {exception.__class__.__name__}: {exception}",
                exc_info=True
            )
            logger.error(f"Logging error: {log_error}")

    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or 'Unknown'

    def _get_user_context(self, request: HttpRequest) -> Dict[str, Any]:
        """Get user context information."""
        if hasattr(request, 'user') and request.user.is_authenticated:
            return {
                'id': request.user.id,
                'email': request.user.email,
                'is_staff': request.user.is_staff,
                'is_superuser': request.user.is_superuser,
            }
        return {'authenticated': False}

    def _get_organization_context(self, request: HttpRequest) -> Dict[str, Any]:
        """Get organization context information."""
        if (hasattr(request, 'user') and 
            hasattr(request.user, 'organization') and 
            request.user.organization):
            return {
                'id': request.user.organization.id,
                'name': request.user.organization.name,
            }
        return {'organization': None}

    def _create_htmx_error_response(self, request: HttpRequest, exception: ClearBaseException) -> JsonResponse:
        """Create HTMX-specific error response."""
        error_data = {
            'error': True,
            'error_id': exception.error_id,
            'error_code': exception.error_code,
            'type': exception.category,
            'message': exception.user_message,
            'severity': exception.severity,
            'details': exception.details if self.debug else {},
            'timestamp': exception.timestamp.isoformat(),
        }

        # Add debug information if in debug mode
        if self.debug and self.include_traceback:
            error_data['debug'] = {
                'exception_type': exception.__class__.__name__,
                'exception_message': exception.message,
                'traceback': traceback.format_exc()
            }

        # Add HTMX-specific headers
        status_code = self._get_status_code_from_exception(exception)
        response = JsonResponse(error_data, status=status_code)
        response['HX-Response'] = 'true'
        
        return response

    def _create_api_error_response(self, request: HttpRequest, exception: ClearBaseException) -> JsonResponse:
        """Create API-specific error response."""
        return JsonResponse(
            exception.to_json_response(),
            status=self._get_status_code_from_exception(exception)
        )

    def _create_html_error_response(self, request: HttpRequest, exception: ClearBaseException) -> HttpResponse:
        """Create HTML error response using templates."""
        status_code = self._get_status_code_from_exception(exception)
        
        # Choose appropriate template
        template_name = self.error_templates.get(str(status_code), self.error_templates['default'])
        
        context = {
            'error': exception,
            'error_id': exception.error_id,
            'error_code': exception.error_code,
            'message': exception.user_message,
            'severity': exception.severity,
            'status_code': status_code,
            'debug': self.debug,
        }

        # Add debug information if in debug mode
        if self.debug:
            context.update({
                'exception_type': exception.__class__.__name__,
                'exception_message': exception.message,
                'details': exception.details,
                'traceback': traceback.format_exc() if self.include_traceback else None,
            })

        return render(request, template_name, context, status=status_code)

    def _get_status_code_from_exception(self, exception: ClearBaseException) -> int:
        """Get appropriate HTTP status code from exception."""
        if isinstance(exception, ResourceNotFoundError):
            return 404
        elif isinstance(exception, AuthenticationError):
            return 401
        elif isinstance(exception, AuthorizationError):
            return 403
        elif isinstance(exception, ValidationError):
            return 422
        elif exception.severity == 'critical':
            return 500
        else:
            return 500
