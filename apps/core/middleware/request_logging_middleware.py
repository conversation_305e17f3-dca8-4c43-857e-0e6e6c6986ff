"""
Request/Response Logging Middleware for CLEAR Application

This middleware provides comprehensive logging of HTTP requests and responses
with structured JSON formatting and performance metrics.
"""

import json
import time
import uuid
from typing import Any, Dict, Optional

from django.conf import settings
from django.http import HttpRequest, HttpResponse
from django.utils.deprecation import MiddlewareMixin

from apps.core.logging import get_logger

logger = get_logger(__name__)


class RequestLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log HTTP requests and responses with comprehensive context.
    
    Features:
    - Request/response logging with timing
    - Correlation ID tracking
    - User and organization context
    - Performance metrics
    - Configurable log levels and filtering
    - Security-aware data sanitization
    """

    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.log_requests = getattr(settings, 'LOG_REQUESTS', True)
        self.log_responses = getattr(settings, 'LOG_RESPONSES', True)
        self.log_request_body = getattr(settings, 'LOG_REQUEST_BODY', False)
        self.log_response_body = getattr(settings, 'LOG_RESPONSE_BODY', False)
        self.max_body_size = getattr(settings, 'MAX_LOG_BODY_SIZE', 1024)
        self.sensitive_headers = getattr(settings, 'SENSITIVE_HEADERS', [
            'authorization', 'cookie', 'x-api-key', 'x-auth-token'
        ])
        self.excluded_paths = getattr(settings, 'LOG_EXCLUDED_PATHS', [
            '/health/', '/metrics/', '/static/', '/media/'
        ])

    def process_request(self, request: HttpRequest) -> None:
        """Process incoming request and set up logging context."""
        # Skip excluded paths
        if any(request.path.startswith(path) for path in self.excluded_paths):
            return

        # Generate correlation ID
        correlation_id = str(uuid.uuid4())
        request.correlation_id = correlation_id
        request.start_time = time.time()

        # Set correlation ID in logger
        logger.set_correlation_id(correlation_id)

        # Log request if enabled
        if self.log_requests:
            self._log_request(request)

    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """Process outgoing response and log metrics."""
        # Skip excluded paths
        if any(request.path.startswith(path) for path in self.excluded_paths):
            return response

        # Calculate request duration
        duration = None
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time

        # Log response if enabled
        if self.log_responses:
            self._log_response(request, response, duration)

        # Add correlation ID to response headers in debug mode
        if getattr(settings, 'DEBUG', False) and hasattr(request, 'correlation_id'):
            response['X-Correlation-ID'] = request.correlation_id

        return response

    def _log_request(self, request: HttpRequest) -> None:
        """Log incoming HTTP request."""
        try:
            # Build request context
            request_context = {
                'correlation_id': getattr(request, 'correlation_id', None),
                'method': request.method,
                'path': request.get_full_path(),
                'content_type': request.content_type,
                'content_length': request.META.get('CONTENT_LENGTH'),
                'user_agent': request.headers.get('User-Agent'),
                'ip_address': self._get_client_ip(request),
                'referer': request.headers.get('Referer'),
                'is_secure': request.is_secure(),
                'is_ajax': request.headers.get('X-Requested-With') == 'XMLHttpRequest',
                'is_htmx': request.headers.get('HX-Request') == 'true',
                'headers': self._sanitize_headers(dict(request.headers)),
            }

            # Add request body if enabled and appropriate
            if (self.log_request_body and 
                request.method in ['POST', 'PUT', 'PATCH'] and
                request.content_type in ['application/json', 'application/x-www-form-urlencoded']):
                
                body = self._get_request_body(request)
                if body:
                    request_context['body'] = body

            # Add user context
            user_context = self._get_user_context(request)
            
            # Add organization context
            organization_context = self._get_organization_context(request)

            # Log the request
            logger.info(
                f"HTTP Request: {request.method} {request.get_full_path()}",
                extra={
                    'request_context': request_context,
                    'user_context': user_context,
                    'organization_context': organization_context,
                }
            )

        except Exception as e:
            logger.error(f"Failed to log request: {e}", exc_info=True)

    def _log_response(self, request: HttpRequest, response: HttpResponse, 
                     duration: Optional[float] = None) -> None:
        """Log outgoing HTTP response."""
        try:
            # Build response context
            response_context = {
                'correlation_id': getattr(request, 'correlation_id', None),
                'status_code': response.status_code,
                'content_type': response.get('Content-Type'),
                'content_length': len(response.content) if hasattr(response, 'content') else None,
                'headers': dict(response.items()),
            }

            # Add timing information
            if duration is not None:
                response_context['duration_ms'] = round(duration * 1000, 2)
                response_context['duration_seconds'] = round(duration, 3)

            # Add response body if enabled and appropriate
            if (self.log_response_body and 
                response.get('Content-Type', '').startswith('application/json')):
                
                body = self._get_response_body(response)
                if body:
                    response_context['body'] = body

            # Determine log level based on status code
            if response.status_code >= 500:
                log_level = 'error'
            elif response.status_code >= 400:
                log_level = 'warning'
            else:
                log_level = 'info'

            # Log the response
            getattr(logger, log_level)(
                f"HTTP Response: {request.method} {request.get_full_path()} -> {response.status_code}",
                extra={
                    'response_context': response_context,
                    'request_method': request.method,
                    'request_path': request.get_full_path(),
                }
            )

            # Log performance warning for slow requests
            if duration and duration > 2.0:
                logger.warning(
                    f"Slow request detected: {request.method} {request.get_full_path()} took {duration:.3f}s",
                    extra={
                        'performance_context': {
                            'operation': f"{request.method} {request.get_full_path()}",
                            'duration_seconds': duration,
                            'threshold_exceeded': True,
                        }
                    }
                )

        except Exception as e:
            logger.error(f"Failed to log response: {e}", exc_info=True)

    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or 'unknown'

    def _get_user_context(self, request: HttpRequest) -> Dict[str, Any]:
        """Get user context information."""
        if hasattr(request, 'user') and request.user.is_authenticated:
            return {
                'user_id': request.user.id,
                'user_email': request.user.email,
                'is_staff': request.user.is_staff,
                'is_superuser': request.user.is_superuser,
                'last_login': request.user.last_login.isoformat() if request.user.last_login else None,
            }
        return {'authenticated': False}

    def _get_organization_context(self, request: HttpRequest) -> Dict[str, Any]:
        """Get organization context information."""
        if (hasattr(request, 'user') and 
            hasattr(request.user, 'organization') and 
            request.user.organization):
            return {
                'organization_id': request.user.organization.id,
                'organization_name': request.user.organization.name,
            }
        return {'organization': None}

    def _sanitize_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """Sanitize sensitive headers for logging."""
        sanitized = {}
        for key, value in headers.items():
            if key.lower() in self.sensitive_headers:
                sanitized[key] = '[REDACTED]'
            else:
                sanitized[key] = value
        return sanitized

    def _get_request_body(self, request: HttpRequest) -> Optional[Dict[str, Any]]:
        """Get request body for logging."""
        try:
            if hasattr(request, 'body') and request.body:
                body_size = len(request.body)
                if body_size > self.max_body_size:
                    return {
                        'truncated': True,
                        'size': body_size,
                        'content': request.body[:self.max_body_size].decode('utf-8', errors='ignore')
                    }
                
                if request.content_type == 'application/json':
                    return json.loads(request.body.decode('utf-8'))
                elif request.content_type == 'application/x-www-form-urlencoded':
                    return dict(request.POST)
                else:
                    return {'raw': request.body.decode('utf-8', errors='ignore')}
        except Exception:
            return {'error': 'Failed to parse request body'}
        
        return None

    def _get_response_body(self, response: HttpResponse) -> Optional[Dict[str, Any]]:
        """Get response body for logging."""
        try:
            if hasattr(response, 'content') and response.content:
                body_size = len(response.content)
                if body_size > self.max_body_size:
                    return {
                        'truncated': True,
                        'size': body_size,
                        'content': response.content[:self.max_body_size].decode('utf-8', errors='ignore')
                    }
                
                content_type = response.get('Content-Type', '')
                if content_type.startswith('application/json'):
                    return json.loads(response.content.decode('utf-8'))
                else:
                    return {'raw': response.content.decode('utf-8', errors='ignore')}
        except Exception:
            return {'error': 'Failed to parse response body'}
        
        return None


class PerformanceLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log performance metrics and database query information.
    """

    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.log_db_queries = getattr(settings, 'LOG_DB_QUERIES', False)
        self.slow_query_threshold = getattr(settings, 'SLOW_QUERY_THRESHOLD', 0.1)

    def process_request(self, request: HttpRequest) -> None:
        """Set up performance tracking."""
        request.performance_start = time.time()
        
        if self.log_db_queries:
            from django.db import connection
            request.queries_start = len(connection.queries)

    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """Log performance metrics."""
        if not hasattr(request, 'performance_start'):
            return response

        # Calculate total request time
        total_time = time.time() - request.performance_start

        # Build performance context
        performance_context = {
            'total_time_ms': round(total_time * 1000, 2),
            'status_code': response.status_code,
            'method': request.method,
            'path': request.get_full_path(),
        }

        # Add database query information if enabled
        if self.log_db_queries and hasattr(request, 'queries_start'):
            from django.db import connection
            
            queries_count = len(connection.queries) - request.queries_start
            queries_time = sum(float(query['time']) for query in connection.queries[request.queries_start:])
            
            performance_context.update({
                'db_queries_count': queries_count,
                'db_queries_time_ms': round(queries_time * 1000, 2),
            })

            # Log slow queries
            slow_queries = [
                query for query in connection.queries[request.queries_start:]
                if float(query['time']) > self.slow_query_threshold
            ]
            
            if slow_queries:
                performance_context['slow_queries'] = [
                    {
                        'sql': query['sql'][:200] + '...' if len(query['sql']) > 200 else query['sql'],
                        'time_ms': round(float(query['time']) * 1000, 2)
                    }
                    for query in slow_queries
                ]

        # Log performance metrics
        if total_time > 1.0:
            logger.warning(
                f"Slow request: {request.method} {request.get_full_path()} took {total_time:.3f}s",
                extra={'performance_context': performance_context}
            )
        else:
            logger.debug(
                f"Request performance: {request.method} {request.get_full_path()}",
                extra={'performance_context': performance_context}
            )

        return response
