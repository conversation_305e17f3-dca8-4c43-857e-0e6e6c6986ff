"""
Django Management Command: Run Responsive Design Tests

This command runs comprehensive responsive design tests across all device sizes
using the Playwright-based testing framework.

Usage:
    python manage.py run_responsive_tests
    python manage.py run_responsive_tests --components navigation,sidebar
    python manage.py run_responsive_tests --viewports mobile_small,tablet_portrait
    python manage.py run_responsive_tests --output-format html
    python manage.py run_responsive_tests --headless false

Created: 2025-07-28
Task: #46 - Add responsive design testing across all device sizes
"""

import os
import sys
import time
import json
from pathlib import Path
from typing import List, Dict, Any, Optional

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.test.utils import get_runner
from django.test.runner import DiscoverRunner

try:
    from playwright.sync_api import sync_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False


class Command(BaseCommand):
    """Django management command for running responsive design tests."""

    help = 'Run comprehensive responsive design tests across all device sizes'

    def add_arguments(self, parser):
        """Add command line arguments."""
        parser.add_argument(
            '--components',
            type=str,
            help='Comma-separated list of components to test (navigation,sidebar,grid_layout,cards,tables,forms)',
            default='all'
        )

        parser.add_argument(
            '--viewports',
            type=str,
            help='Comma-separated list of viewports to test',
            default='all'
        )

        parser.add_argument(
            '--output-format',
            choices=['json', 'html', 'both'],
            default='both',
            help='Output format for test reports'
        )

        parser.add_argument(
            '--headless',
            type=str,
            choices=['true', 'false'],
            default='true',
            help='Run browser in headless mode'
        )

        parser.add_argument(
            '--output-dir',
            type=str,
            help='Directory to save test reports',
            default=None
        )

        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output'
        )

        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be tested without running tests'
        )

    def handle(self, *args, **options):
        """Handle the command execution."""
        if not PLAYWRIGHT_AVAILABLE:
            raise CommandError(
                "Playwright is not available. Install it with: pip install playwright"
            )

        self.verbosity = options.get('verbosity', 1)
        self.verbose = options.get('verbose', False)

        # Parse components
        components = self._parse_components(options['components'])

        # Parse viewports
        viewports = self._parse_viewports(options['viewports'])

        # Setup output directory
        output_dir = self._setup_output_directory(options['output_dir'])

        # Show configuration
        self._show_configuration(components, viewports, options, output_dir)

        if options['dry_run']:
            self.stdout.write(
                self.style.SUCCESS('Dry run completed. Use --dry-run=false to run actual tests.')
            )
            return

        # Run tests
        try:
            results = self._run_responsive_tests(
                components=components,
                viewports=viewports,
                headless=options['headless'] == 'true',
                output_dir=output_dir,
                output_format=options['output_format']
            )

            self._display_results(results)

        except Exception as e:
            raise CommandError(f"Test execution failed: {str(e)}")

    def _parse_components(self, components_str: str) -> List[str]:
        """Parse components argument."""
        available_components = [
            'navigation', 'sidebar', 'grid_layout', 'cards', 'tables', 'forms'
        ]

        if components_str.lower() == 'all':
            return available_components

        components = [c.strip() for c in components_str.split(',')]

        # Validate components
        invalid_components = [c for c in components if c not in available_components]
        if invalid_components:
            raise CommandError(
                f"Invalid components: {', '.join(invalid_components)}. "
                f"Available: {', '.join(available_components)}"
            )

        return components

    def _parse_viewports(self, viewports_str: str) -> List[str]:
        """Parse viewports argument."""
        available_viewports = [
            'mobile_small', 'mobile_medium', 'mobile_large',
            'tablet_portrait', 'tablet_landscape', 'tablet_large',
            'desktop_small', 'desktop_medium', 'desktop_large', 'desktop_ultra'
        ]

        if viewports_str.lower() == 'all':
            return available_viewports

        viewports = [v.strip() for v in viewports_str.split(',')]

        # Validate viewports
        invalid_viewports = [v for v in viewports if v not in available_viewports]
        if invalid_viewports:
            raise CommandError(
                f"Invalid viewports: {', '.join(invalid_viewports)}. "
                f"Available: {', '.join(available_viewports)}"
            )

        return viewports

    def _setup_output_directory(self, output_dir: Optional[str]) -> Path:
        """Setup output directory for test reports."""
        if output_dir:
            output_path = Path(output_dir)
        else:
            output_path = Path(settings.BASE_DIR) / 'tests' / 'responsive_design' / 'reports'

        output_path.mkdir(parents=True, exist_ok=True)
        return output_path

    def _show_configuration(
        self,
        components: List[str],
        viewports: List[str],
        options: Dict[str, Any],
        output_dir: Path
    ):
        """Display test configuration."""
        self.stdout.write(self.style.SUCCESS('=== Responsive Design Test Configuration ==='))
        self.stdout.write(f"Components to test: {', '.join(components)}")
        self.stdout.write(f"Viewports to test: {', '.join(viewports)}")
        self.stdout.write(f"Total test combinations: {len(components) * len(viewports)}")
        self.stdout.write(f"Headless mode: {options['headless']}")
        self.stdout.write(f"Output format: {options['output_format']}")
        self.stdout.write(f"Output directory: {output_dir}")
        self.stdout.write('')

    def _run_responsive_tests(
        self,
        components: List[str],
        viewports: List[str],
        headless: bool,
        output_dir: Path,
        output_format: str
    ) -> Dict[str, Any]:
        """Run the responsive design tests."""
        self.stdout.write(self.style.SUCCESS('Starting responsive design tests...'))

        # Import test framework
        sys.path.insert(0, str(Path(settings.BASE_DIR) / 'tests'))

        try:
            from responsive_design.test_responsive_design import (
                ResponsiveDesignConfig,
                ResponsiveDesignTester,
                ResponsiveTestResult
            )
        except ImportError as e:
            raise CommandError(f"Could not import responsive design test framework: {e}")

        # Initialize configuration
        config = ResponsiveDesignConfig()
        config.REPORTS_DIR = output_dir
        config.ensure_directories()

        # Initialize Playwright
        playwright = sync_playwright().start()
        browser = playwright.chromium.launch(headless=headless)
        context = browser.new_context()
        page = context.new_page()

        tester = ResponsiveDesignTester(page, config)
        test_results = []

        try:
            # Get Django test server URL
            from django.test import LiveServerTestCase
            from django.contrib.staticfiles.testing import StaticLiveServerTestCase

            # Create a temporary test server
            test_server = StaticLiveServerTestCase()
            test_server._pre_setup()

            base_url = test_server.live_server_url

            total_tests = len(components) * len(viewports)
            current_test = 0

            # Run tests for each component and viewport combination
            for component_name in components:
                for viewport_name in viewports:
                    current_test += 1

                    if self.verbose or self.verbosity >= 2:
                        self.stdout.write(
                            f"Running test {current_test}/{total_tests}: "
                            f"{component_name} @ {viewport_name}"
                        )

                    # Determine URL path based on component
                    url_path = self._get_url_path_for_component(component_name)

                    try:
                        result = tester.test_viewport_behavior(
                            f"{base_url}{url_path}",
                            component_name,
                            viewport_name
                        )
                        test_results.append(result)

                        # Show progress
                        status = "PASS" if result.passed else "FAIL"
                        status_style = self.style.SUCCESS if result.passed else self.style.ERROR

                        if self.verbose or self.verbosity >= 2:
                            self.stdout.write(f"  Result: {status_style(status)}")
                        elif self.verbosity >= 1:
                            self.stdout.write('.', ending='')
                            if current_test % 50 == 0:
                                self.stdout.write('')

                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f"  Error: {str(e)}")
                        )
                        # Create failed result
                        result = ResponsiveTestResult(
                            test_name=f"{component_name}_{viewport_name}",
                            viewport_name=viewport_name,
                            viewport_size=(0, 0),
                            component_name=component_name,
                            layout_valid=False,
                            breakpoint_behavior={},
                            accessibility_score=0.0,
                            performance_metrics={},
                            issues_found=[str(e)],
                            passed=False
                        )
                        test_results.append(result)

            # Cleanup test server
            test_server._post_teardown()

        finally:
            # Cleanup Playwright
            context.close()
            browser.close()
            playwright.stop()

        # Generate reports
        report_data = self._generate_report_data(test_results, config)

        if output_format in ['json', 'both']:
            self._save_json_report(report_data, output_dir)

        if output_format in ['html', 'both']:
            self._save_html_report(report_data, output_dir)

        return report_data

    def _get_url_path_for_component(self, component_name: str) -> str:
        """Get appropriate URL path for testing component."""
        url_mapping = {
            'navigation': '/dashboard/',
            'sidebar': '/dashboard/',
            'grid_layout': '/projects/',
            'cards': '/projects/',
            'tables': '/projects/',
            'forms': '/projects/create/'
        }
        return url_mapping.get(component_name, '/dashboard/')

    def _generate_report_data(self, test_results: List, config) -> Dict[str, Any]:
        """Generate comprehensive report data."""
        report_data = {
            'test_summary': {
                'total_tests': len(test_results),
                'passed_tests': sum(1 for r in test_results if r.passed),
                'failed_tests': sum(1 for r in test_results if not r.passed),
                'test_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'framework': 'Playwright + Django Management Command',
                'viewports_tested': list(set(r.viewport_name for r in test_results))
            },
            'viewport_analysis': {},
            'component_analysis': {},
            'detailed_results': []
        }

        # Analyze results by viewport
        for viewport_name in set(r.viewport_name for r in test_results):
            viewport_results = [r for r in test_results if r.viewport_name == viewport_name]
            report_data['viewport_analysis'][viewport_name] = {
                'total_tests': len(viewport_results),
                'passed_tests': sum(1 for r in viewport_results if r.passed),
                'avg_accessibility_score': sum(r.accessibility_score for r in viewport_results) / len(viewport_results) if viewport_results else 0,
                'device_info': config.VIEWPORT_SIZES.get(viewport_name, {})
            }

        # Analyze results by component
        for component_name in set(r.component_name for r in test_results):
            component_results = [r for r in test_results if r.component_name == component_name]
            report_data['component_analysis'][component_name] = {
                'total_tests': len(component_results),
                'passed_tests': sum(1 for r in component_results if r.passed),
                'avg_accessibility_score': sum(r.accessibility_score for r in component_results) / len(component_results) if component_results else 0,
                'problematic_viewports': [r.viewport_name for r in component_results if not r.passed]
            }

        # Add detailed results
        for result in test_results:
            report_data['detailed_results'].append({
                'test_name': result.test_name,
                'viewport_name': result.viewport_name,
                'viewport_size': result.viewport_size,
                'component_name': result.component_name,
                'passed': result.passed,
                'layout_valid': result.layout_valid,
                'accessibility_score': result.accessibility_score,
                'breakpoint_behavior': result.breakpoint_behavior,
                'performance_metrics': result.performance_metrics,
                'issues_found': result.issues_found
            })

        return report_data

    def _save_json_report(self, report_data: Dict[str, Any], output_dir: Path):
        """Save JSON report."""
        report_path = output_dir / f"responsive_design_report_{int(time.time())}.json"
        with open(report_path, 'w') as f:
            json.dump(report_data, f, indent=2)

        self.stdout.write(
            self.style.SUCCESS(f"JSON report saved to: {report_path}")
        )

    def _save_html_report(self, report_data: Dict[str, Any], output_dir: Path):
        """Save HTML report."""
        html_content = self._generate_html_report(report_data)
        report_path = output_dir / f"responsive_design_report_{int(time.time())}.html"

        with open(report_path, 'w') as f:
            f.write(html_content)

        self.stdout.write(
            self.style.SUCCESS(f"HTML report saved to: {report_path}")
        )

    def _generate_html_report(self, report_data: Dict[str, Any]) -> str:
        """Generate HTML report."""
        test_date = report_data['test_summary']['test_date']
        framework = report_data['test_summary']['framework']
        total_tests = report_data['test_summary']['total_tests']
        passed_tests = report_data['test_summary']['passed_tests']
        failed_tests = report_data['test_summary']['failed_tests']

        # Calculate pass rate for progress bar
        pass_percentage = (passed_tests / max(total_tests, 1)) * 100

        html_parts = []

        # HTML header
        html_parts.append(f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>CLEAR Responsive Design Test Report</title>
            <style>
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                .header {{ background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }}
                .header h1 {{ margin: 0; font-size: 2.5em; }}
                .header p {{ margin: 10px 0 0 0; opacity: 0.9; }}
                .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }}
                .card {{ background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .card h3 {{ margin: 0 0 15px 0; color: #495057; font-size: 1.1em; }}
                .card .number {{ font-size: 3em; font-weight: bold; margin: 0; }}
                .passed .number {{ color: #28a745; }}
                .failed .number {{ color: #dc3545; }}
                .total .number {{ color: #007bff; }}
                .section {{ background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px; }}
                .section h2 {{ margin: 0 0 25px 0; color: #343a40; border-bottom: 2px solid #007bff; padding-bottom: 10px; }}
                .viewport-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
                .viewport-card {{ background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }}
                .viewport-card h4 {{ margin: 0 0 15px 0; color: #495057; }}
                .viewport-card p {{ margin: 5px 0; color: #6c757d; }}
                table {{ width: 100%; border-collapse: collapse; }}
                th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; }}
                th {{ background-color: #f8f9fa; font-weight: 600; color: #495057; }}
                .status-pass {{ color: #28a745; font-weight: bold; }}
                .status-fail {{ color: #dc3545; font-weight: bold; }}
                .progress-bar {{ background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden; margin: 10px 0; }}
                .progress-fill {{ background: #28a745; height: 100%; transition: width 0.3s ease; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>CLEAR Responsive Design Test Report</h1>
                    <p>Generated: {test_date} | Framework: {framework}</p>
                </div>

                <div class="summary">
                    <div class="card total">
                        <h3>Total Tests</h3>
                        <p class="number">{total_tests}</p>
                    </div>
                    <div class="card passed">
                        <h3>Passed</h3>
                        <p class="number">{passed_tests}</p>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {pass_percentage:.1f}%"></div>
                        </div>
                    </div>
                    <div class="card failed">
                        <h3>Failed</h3>
                        <p class="number">{failed_tests}</p>
                    </div>
                </div>

                <div class="section">
                    <h2>Viewport Analysis</h2>
                    <div class="viewport-grid">
        """)

        # Viewport analysis
        for viewport_name, analysis in report_data['viewport_analysis'].items():
            device_info = analysis.get('device_info', {})
            device_name = device_info.get('device', 'Unknown Device')
            width = device_info.get('width', 0)
            height = device_info.get('height', 0)

            viewport_pass_rate = (analysis['passed_tests'] / max(analysis['total_tests'], 1)) * 100
            viewport_title = viewport_name.replace('_', ' ').title()

            html_parts.append(f"""
                        <div class="viewport-card">
                            <h4>{viewport_title}</h4>
                            <p><strong>Device:</strong> {device_name}</p>
                            <p><strong>Resolution:</strong> {width} × {height}px</p>
                            <p><strong>Tests:</strong> {analysis['passed_tests']}/{analysis['total_tests']} passed ({viewport_pass_rate:.1f}%)</p>
                            <p><strong>Avg Accessibility:</strong> {analysis['avg_accessibility_score']:.2f}/1.0</p>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: {viewport_pass_rate:.1f}%"></div>
                            </div>
                        </div>
            """)

        # Component analysis section
        html_parts.append("""
                    </div>
                </div>

                <div class="section">
                    <h2>Component Analysis</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>Component</th>
                                <th>Tests Passed</th>
                                <th>Pass Rate</th>
                                <th>Avg Accessibility Score</th>
                                <th>Problematic Viewports</th>
                            </tr>
                        </thead>
                        <tbody>
        """)

        # Component analysis rows
        for component_name, analysis in report_data['component_analysis'].items():
            component_pass_rate = (analysis['passed_tests'] / max(analysis['total_tests'], 1)) * 100
            problematic = ', '.join(analysis['problematic_viewports']) if analysis['problematic_viewports'] else 'None'
            component_title = component_name.replace('_', ' ').title()

            html_parts.append(f"""
                            <tr>
                                <td><strong>{component_title}</strong></td>
                                <td>{analysis['passed_tests']}/{analysis['total_tests']}</td>
                                <td>{component_pass_rate:.1f}%</td>
                                <td>{analysis['avg_accessibility_score']:.2f}</td>
                                <td>{problematic}</td>
                            </tr>
            """)

        # Detailed results section
        html_parts.append("""
                        </tbody>
                    </table>
                </div>

                <div class="section">
                    <h2>Detailed Test Results</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>Test Name</th>
                                <th>Viewport</th>
                                <th>Component</th>
                                <th>Status</th>
                                <th>Layout Valid</th>
                                <th>Accessibility Score</th>
                            </tr>
                        </thead>
                        <tbody>
        """)

        # Detailed results rows
        for result in report_data['detailed_results']:
            status_class = 'status-pass' if result['passed'] else 'status-fail'
            status_text = 'PASS' if result['passed'] else 'FAIL'
            viewport_title = result['viewport_name'].replace('_', ' ').title()
            component_title = result['component_name'].replace('_', ' ').title()
            layout_icon = '✓' if result['layout_valid'] else '✗'

            html_parts.append(f"""
                            <tr>
                                <td>{result['test_name']}</td>
                                <td>{viewport_title}</td>
                                <td>{component_title}</td>
                                <td class="{status_class}">{status_text}</td>
                                <td>{layout_icon}</td>
                                <td>{result['accessibility_score']:.2f}</td>
                            </tr>
            """)

        # HTML footer
        html_parts.append("""
                        </tbody>
                    </table>
                </div>
            </div>
        </body>
        </html>
        """)

        return ''.join(html_parts)

    def _display_results(self, results: Dict[str, Any]):
        """Display test results summary."""
        summary = results['test_summary']

        self.stdout.write('')
        self.stdout.write(self.style.SUCCESS('=== Test Results Summary ==='))
        self.stdout.write(f"Total tests: {summary['total_tests']}")
        self.stdout.write(f"Passed: {self.style.SUCCESS(str(summary['passed_tests']))}")
        self.stdout.write(f"Failed: {self.style.ERROR(str(summary['failed_tests']))}")

        if summary['total_tests'] > 0:
            pass_rate = (summary['passed_tests'] / summary['total_tests']) * 100
            self.stdout.write(f"Pass rate: {pass_rate:.1f}%")

        # Show component summary
        self.stdout.write('')
        self.stdout.write('Component Results:')
        for component_name, analysis in results['component_analysis'].items():
            pass_rate = (analysis['passed_tests'] / max(analysis['total_tests'], 1)) * 100
            status_style = self.style.SUCCESS if pass_rate == 100 else self.style.WARNING if pass_rate >= 80 else self.style.ERROR

            passed_count = analysis['passed_tests']
            total_count = analysis['total_tests']
            component_title = component_name.replace('_', ' ').title()
            result_text = f'{passed_count}/{total_count} ({pass_rate:.1f}%)'

            self.stdout.write(f"  {component_title}: {status_style(result_text)}")

        self.stdout.write('')
        if summary['failed_tests'] == 0:
            self.stdout.write(self.style.SUCCESS('All responsive design tests passed! 🎉'))
        else:
            failed_count = summary['failed_tests']
            self.stdout.write(
                self.style.WARNING(f'{failed_count} tests failed. Check the detailed report for more information.')
            )
