"""
Error Handling Decorators for CLEAR Application

This module provides decorators for standardized error handling in views,
integrating with the CLEAR exception hierarchy and logging system.
"""

import functools
import time
from typing import Any, Callable, Dict, Optional, Type, Union

from django.core.exceptions import PermissionDenied, ValidationError as DjangoValidationError
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import render
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_exempt

from apps.core.exceptions import (
    ClearBaseException,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    ResourceNotFoundError,
    convert_django_exception,
    handle_exception_chain,
)
from apps.core.logging import get_logger

logger = get_logger(__name__)


def handle_exceptions(
    exceptions: Optional[Dict[Type[Exception], Type[ClearBaseException]]] = None,
    log_exceptions: bool = True,
    include_performance_logging: bool = False
):
    """
    Decorator to handle exceptions in views with standardized error handling.
    
    Args:
        exceptions: Mapping of exception types to CLEAR exception types
        log_exceptions: Whether to log exceptions
        include_performance_logging: Whether to log performance metrics
    
    Usage:
        @handle_exceptions({
            ValueError: ValidationError,
            KeyError: ResourceNotFoundError,
        })
        def my_view(request):
            # View logic here
            pass
    """
    def decorator(view_func: Callable) -> Callable:
        @functools.wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs) -> HttpResponse:
            start_time = time.time() if include_performance_logging else None
            
            try:
                # Execute the view
                response = view_func(request, *args, **kwargs)
                
                # Log performance if requested
                if include_performance_logging and start_time:
                    duration = time.time() - start_time
                    logger.log_performance(
                        f"{view_func.__name__}",
                        duration,
                        {'view': view_func.__name__, 'args': args, 'kwargs': kwargs}
                    )
                
                return response
                
            except Exception as exc:
                # Convert to CLEAR exception if needed
                if isinstance(exc, ClearBaseException):
                    clear_exception = exc
                elif exceptions and type(exc) in exceptions:
                    # Use custom mapping
                    clear_exception_class = exceptions[type(exc)]
                    clear_exception = clear_exception_class(
                        str(exc),
                        details={'original_exception': str(exc)}
                    )
                elif isinstance(exc, (DjangoValidationError, PermissionDenied)):
                    clear_exception = convert_django_exception(exc)
                else:
                    clear_exception = handle_exception_chain(exc)
                
                # Log the exception if requested
                if log_exceptions:
                    logger.log_exception(clear_exception, request)
                
                # Let the middleware handle the response
                raise clear_exception
        
        return wrapper
    return decorator


def require_organization_access(view_func: Callable) -> Callable:
    """
    Decorator to ensure user has access to the organization context.
    
    Usage:
        @require_organization_access
        def my_view(request):
            # User is guaranteed to have organization access
            pass
    """
    @functools.wraps(view_func)
    def wrapper(request: HttpRequest, *args, **kwargs) -> HttpResponse:
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            raise AuthenticationError("Authentication required")
        
        if not hasattr(request.user, 'organization') or not request.user.organization:
            raise AuthorizationError(
                "Organization access required",
                user_message="You must be associated with an organization to access this resource."
            )
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


def validate_request_data(
    required_fields: Optional[list] = None,
    optional_fields: Optional[list] = None,
    validation_rules: Optional[Dict[str, Callable]] = None
):
    """
    Decorator to validate request data with custom rules.
    
    Args:
        required_fields: List of required field names
        optional_fields: List of optional field names
        validation_rules: Dict mapping field names to validation functions
    
    Usage:
        @validate_request_data(
            required_fields=['name', 'email'],
            validation_rules={'email': lambda x: '@' in x}
        )
        def my_view(request):
            # Request data is validated
            pass
    """
    def decorator(view_func: Callable) -> Callable:
        @functools.wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs) -> HttpResponse:
            # Get request data based on method
            if request.method == 'GET':
                data = request.GET
            else:
                data = request.POST
            
            errors = {}
            
            # Check required fields
            if required_fields:
                for field in required_fields:
                    if field not in data or not data[field].strip():
                        errors[field] = f"Field '{field}' is required"
            
            # Validate fields with custom rules
            if validation_rules:
                for field, validator in validation_rules.items():
                    if field in data and data[field]:
                        try:
                            if not validator(data[field]):
                                errors[field] = f"Field '{field}' is invalid"
                        except Exception as e:
                            errors[field] = f"Validation error for '{field}': {str(e)}"
            
            # Raise validation error if any errors found
            if errors:
                raise ValidationError(
                    "Request data validation failed",
                    details={'field_errors': errors},
                    user_message="Please check your input and try again."
                )
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def log_business_action(action_type: str, extract_data: Optional[Callable] = None):
    """
    Decorator to log business actions for auditing.
    
    Args:
        action_type: Type of business action
        extract_data: Function to extract relevant data from request/response
    
    Usage:
        @log_business_action('project_created')
        def create_project(request):
            # Business action will be logged
            pass
    """
    def decorator(view_func: Callable) -> Callable:
        @functools.wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs) -> HttpResponse:
            response = view_func(request, *args, **kwargs)
            
            # Extract business data
            business_data = {'action': action_type}
            if extract_data:
                try:
                    extracted = extract_data(request, response, *args, **kwargs)
                    if extracted:
                        business_data.update(extracted)
                except Exception as e:
                    logger.warning(f"Failed to extract business data: {e}")
            
            # Log the business action
            logger.log_business_event(action_type, business_data, request)
            
            return response
        
        return wrapper
    return decorator


def cache_on_success(timeout: int = 300, key_func: Optional[Callable] = None):
    """
    Decorator to cache successful responses.
    
    Args:
        timeout: Cache timeout in seconds
        key_func: Function to generate cache key
    
    Usage:
        @cache_on_success(timeout=600)
        def expensive_view(request):
            # Response will be cached on success
            pass
    """
    def decorator(view_func: Callable) -> Callable:
        @functools.wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs) -> HttpResponse:
            from django.core.cache import cache
            
            # Generate cache key
            if key_func:
                cache_key = key_func(request, *args, **kwargs)
            else:
                cache_key = f"view_cache:{view_func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # Try to get from cache
            cached_response = cache.get(cache_key)
            if cached_response:
                return cached_response
            
            # Execute view
            response = view_func(request, *args, **kwargs)
            
            # Cache successful responses
            if hasattr(response, 'status_code') and 200 <= response.status_code < 300:
                cache.set(cache_key, response, timeout)
            
            return response
        
        return wrapper
    return decorator


# Class-based view decorators
class ErrorHandlingMixin:
    """
    Mixin for class-based views to add standardized error handling.
    """
    
    exception_mapping: Dict[Type[Exception], Type[ClearBaseException]] = {}
    log_exceptions: bool = True
    include_performance_logging: bool = False
    
    def dispatch(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        """Override dispatch to add error handling."""
        start_time = time.time() if self.include_performance_logging else None
        
        try:
            response = super().dispatch(request, *args, **kwargs)
            
            # Log performance if requested
            if self.include_performance_logging and start_time:
                duration = time.time() - start_time
                logger.log_performance(
                    f"{self.__class__.__name__}.{request.method.lower()}",
                    duration,
                    {'view_class': self.__class__.__name__, 'method': request.method}
                )
            
            return response
            
        except Exception as exc:
            # Convert to CLEAR exception if needed
            if isinstance(exc, ClearBaseException):
                clear_exception = exc
            elif self.exception_mapping and type(exc) in self.exception_mapping:
                clear_exception_class = self.exception_mapping[type(exc)]
                clear_exception = clear_exception_class(
                    str(exc),
                    details={'original_exception': str(exc)}
                )
            elif isinstance(exc, (DjangoValidationError, PermissionDenied)):
                clear_exception = convert_django_exception(exc)
            else:
                clear_exception = handle_exception_chain(exc)
            
            # Log the exception if requested
            if self.log_exceptions:
                logger.log_exception(clear_exception, request)
            
            # Let the middleware handle the response
            raise clear_exception


# Method decorator for class-based views
def method_error_handler(**decorator_kwargs):
    """
    Method decorator for class-based views.
    
    Usage:
        class MyView(View):
            @method_error_handler(log_exceptions=True)
            def get(self, request):
                pass
    """
    def decorator(method):
        return handle_exceptions(**decorator_kwargs)(method)
    return decorator
