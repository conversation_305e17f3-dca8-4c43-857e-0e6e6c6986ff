"""Notification service for CLEAR application.

from requests.exceptions import HTTPError
from datetime import datetime
from datetime import timedelta
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import get_user_model
User = get_user_model()
from django.db import models
from django.utils import timezone
from rest_framework import status
from typing import Optional
import channels
import logging
import logging
logger = logging.getLogger(__name__)
import re
import time

Handles email notifications, batching, and delivery management.
"""

import logging
from datetime import timedelta
from typing import Optional

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils import timezone

from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer

from apps.messaging.models import (
    ChatMessage,
    Conversation,
    ConversationMember,
    Notification,
    NotificationBatch,
    NotificationDelivery,
    NotificationSettings,
)

logger = logging.getLogger(__name__)
User = get_user_model()


class NotificationService:
    """Service for managing notifications across all delivery methods"""

    def __init__(self):
        self.channel_layer = get_channel_layer()

    def create_notification(
        self,
        user: User,
        notification_type: str,
        title: str,
        message: str,
        data: dict | None = None,
        action_url: str | None = None,
        action_label: str | None = None,
        priority: str = "normal",
    ) -> Notification:
        """Create a new notification and schedule delivery"""
        # Create the notification
        notification = Notification.objects.create(
            user=user,
            type=notification_type,
            title=title,
            message=message,
            data=data or {},
            action_url=action_url,
            action_label=action_label,
            priority=priority,
        )

        # Schedule delivery based on user preferences
        self._schedule_notification_delivery(notification)

        return notification

    def _schedule_notification_delivery(self, notification: Notification):
        """Schedule notification delivery based on user preferences"""
        try:
            settings_obj = notification.user.notification_settings
        except NotificationSettings.DoesNotExist:
            # Create default settings
            settings_obj = NotificationSettings.objects.create(user=notification.user)

        # Get notification type preferences
        type_prefs = settings_obj.get_notification_type_preference(notification.type)

        # Schedule browser notification (immediate)
        if settings_obj.browser_notifications and type_prefs.get("browser", True):
            self._deliver_browser_notification(notification)

        # Schedule email notification based on preferences
        if settings_obj.email_notifications and type_prefs.get("email", True):
            if settings_obj.email_immediate and not settings_obj.is_quiet_hours():
                self._deliver_email_notification(notification)
            else:
                self._add_to_email_batch(notification, settings_obj)

    def _deliver_browser_notification(self, notification: Notification):
        """Deliver immediate browser notification via WebSocket"""
        try:
            # Create delivery record
            NotificationDelivery.objects.create(
                notification=notification,
                delivery_method="browser",
                delivery_status="sent",
                delivered_at=timezone.now(),
            )

            # Send via WebSocket
            if self.channel_layer:
                async_to_sync(self.channel_layer.group_send)(
                    f"notifications_{notification.user.id}",
                    {
                        "type": "notification_message",
                        "notification": {
                            "id": str(notification.id),
                            "title": notification.title,
                            "message": notification.message,
                            "type": notification.type,
                            "priority": notification.priority,
                            "action_url": notification.action_url,
                            "action_label": notification.action_label,
                            "created_at": notification.created_at.isoformat(),
                        },
                        "timestamp": timezone.now().isoformat(),
                    },
                )

            logger.info(f"Browser notification delivered to {notification.user.username}")

        except Exception as e:
            logger.exception("Failed to deliver browser notification")
            NotificationDelivery.objects.create(
                notification=notification,
                delivery_method="browser",
                delivery_status="failed",
                delivery_error=str(e),
            )

    def _deliver_email_notification(self, notification: Notification):
        """Deliver immediate email notification"""
        try:
            # Create delivery record
            delivery = NotificationDelivery.objects.create(
                notification=notification,
                delivery_method="email",
                delivery_status="pending",
            )

            # Prepare email
            context = {
                "user": notification.user,
                "notification": notification,
                "site_name": getattr(settings, "SITE_NAME", "CLEAR"),
                "site_url": getattr(settings, "SITE_URL", "http://localhost:8743"),
            }

            subject = f"[CLEAR] {notification.title}"

            # Use HTML template if available, fallback to plain text
            try:
                html_message = render_to_string("shared/emails/notification.html", context)
                plain_message = render_to_string("emails/notification.txt", context)
            except (ConnectionError, TimeoutError, HTTPError):
                html_message = None
                plain_message = notification.message

            # Send email
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=getattr(settings, "DEFAULT_FROM_EMAIL", "<EMAIL>"),
                recipient_list=[notification.user.email],
                html_message=html_message,
                fail_silently=False,
            )

            # Update delivery record
            delivery.delivery_status = "sent"
            delivery.delivered_at = timezone.now()
            delivery.save()

            # Update notification
            notification.email_sent = True
            notification.email_sent_at = timezone.now()
            notification.save()

            logger.info(f"Email notification sent to {notification.user.email}")

        except Exception as e:
            logger.exception("Failed to send email notification")
            delivery.delivery_status = "failed"
            delivery.delivery_error = str(e)
            delivery.save()

    def _add_to_email_batch(self, notification: Notification, settings_obj: NotificationSettings):
        """Add notification to appropriate email batch"""
        try:
            # Determine batch type
            if settings_obj.email_daily_digest:
                batch_type = "daily"
                # Schedule for next morning (8 AM user time)
                next_send = timezone.now().replace(hour=8, minute=0, second=0, microsecond=0)
                if next_send <= timezone.now():
                    next_send += timedelta(days=1)
            elif settings_obj.email_weekly_digest:
                batch_type = "weekly"
                # Schedule for next Monday 8 AM
                days_ahead = 0 - timezone.now().weekday()  # Monday is 0
                if days_ahead <= 0:  # Today is Monday or later
                    days_ahead += 7
                next_send = (timezone.now() + timedelta(days=days_ahead)).replace(
                    hour=8,
                    minute=0,
                    second=0,
                    microsecond=0,
                )
            else:
                # Default to daily if no preference set
                batch_type = "daily"
                next_send = timezone.now().replace(hour=8, minute=0, second=0, microsecond=0)
                if next_send <= timezone.now():
                    next_send += timedelta(days=1)

            # Get or create batch
            batch, created = NotificationBatch.objects.get_or_create(
                user=notification.user,
                batch_type=batch_type,
                batch_status="pending",
                defaults={"scheduled_for": next_send, "notification_count": 0},
            )

            # Add notification to batch
            notification.batch = batch
            notification.save()

            # Update batch count
            batch.notification_count += 1
            batch.save()

            logger.info(
                f"Added notification to {batch_type} batch for {notification.user.username}",
            )

        except Exception:
            logger.exception("Failed to add notification to batch")

    def send_notification_batches(self):
        """Send all pending notification batches that are due"""
        due_batches = NotificationBatch.objects.filter(
            batch_status="pending",
            scheduled_for__lte=timezone.now(),
        ).select_related("user")

        for batch in due_batches:
            try:
                self._send_notification_batch(batch)
            except Exception:
                logger.exception("Failed to send batch {batch.id}")
                batch.batch_status = "failed"
                batch.save()

    def _send_notification_batch(self, batch: NotificationBatch):
        """Send a single notification batch via email"""
        notifications = batch.notifications.filter(read=False).order_by("-created_at")

        if not notifications.exists():
            batch.batch_status = "sent"
            batch.sent_at = timezone.now()
            batch.save()
            return

        # Prepare batch email
        context = {
            "user": batch.user,
            "batch": batch,
            "notifications": notifications,
            "notification_count": notifications.count(),
            "site_name": getattr(settings, "SITE_NAME", "CLEAR"),
            "site_url": getattr(settings, "SITE_URL", "http://localhost:8743"),
        }

        subject = f"[CLEAR] {batch.get_batch_type_display()} - {notifications.count()} notification{
            's' if notifications.count() != 1 else ''
        }"

        try:
            # Use HTML template if available
            html_message = render_to_string("shared/emails/notification_batch.html", context)
            plain_message = render_to_string("emails/notification_batch.txt", context)
        except (FileNotFoundError, PermissionError, OSError):
            # Fallback to simple text
            plain_message = f"You have {notifications.count()} unread notifications in CLEAR."
            html_message = None

        # Send email
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=getattr(settings, "DEFAULT_FROM_EMAIL", "<EMAIL>"),
            recipient_list=[batch.user.email],
            html_message=html_message,
            fail_silently=False,
        )

        # Update batch status
        batch.batch_status = "sent"
        batch.sent_at = timezone.now()
        batch.save()

        # Create delivery records for each notification
        for notification in notifications:
            NotificationDelivery.objects.create(
                notification=notification,
                delivery_method="email",
                delivery_status="sent",
                delivered_at=timezone.now(),
            )
            notification.email_sent = True
            notification.email_sent_at = timezone.now()
            notification.save()

        logger.info(
            f"Sent {batch.batch_type} batch to {batch.user.email} with {notifications.count()} notifications",
        )


class MessageNotificationService:
    """Service for handling message-related notifications"""

    def __init__(self):
        self.notification_service = NotificationService()

    def notify_new_message(self, message: ChatMessage, conversation: Optional["Conversation"] = None):
        """Create notifications for new chat messages"""
        # Get conversation members who should be notified
        if conversation:
            members = (
                ConversationMember.objects.filter(conversation=conversation)
                .exclude(user=message.user)
                .select_related("user")
            )

            for member in members:
                self._create_message_notification(user=member.user, message=message, conversation=conversation)
        elif message.project:
            # Notify project team members
            project_users = User.objects.filter(username=message.project.egis_project_manager).exclude(
                id=message.user.id,
            )

            for user in project_users:
                self._create_message_notification(user=user, message=message)

    def _create_message_notification(
        self,
        user: User,
        message: ChatMessage,
        conversation: Optional["Conversation"] = None,
    ):
        """Create a notification for a new message"""
        if conversation:
            title = f"New message in {conversation.name}"
            action_url = f"/messages/#conversation-{conversation.id}"
        else:
            title = f"New message from {message.user.get_full_name() or message.user.username}"
            action_url = "/messages/"

        # Truncate message content for notification
        message_preview = message.content[:100] + "..." if len(message.content) > 100 else message.content

        self.notification_service.create_notification(
            user=user,
            notification_type="new_message",
            title=title,
            message=message_preview,
            data={
                "message_id": message.id,
                "sender": message.user.username,
                "conversation_id": str(conversation.id) if conversation else None,
                "project_id": str(message.project.id) if message.project else None,
            },
            action_url=action_url,
            action_label="View Message",
            priority="normal",
        )

    def notify_missed_messages(self, user: User, hours_ago: int = 1):
        """Notify user about messages they missed while offline"""
        since_time = timezone.now() - timedelta(hours=hours_ago)

        # Find conversations where user has unread messages
        conversations_with_unread = (
            Conversation.objects.filter(
                members__user=user,
                messages__created_at__gte=since_time,
                messages__read_by__isnull=True,
            )
            .exclude(messages__user=user)
            .distinct()
        )

        if conversations_with_unread.exists():
            total_unread = sum(
                conv.messages.filter(created_at__gte=since_time, read_by__isnull=True).exclude(user=user).count()
                for conv in conversations_with_unread
            )

            if total_unread > 0:
                self.notification_service.create_notification(
                    user=user,
                    notification_type="missed_messages",
                    title=f"You have {total_unread} missed message{'s' if total_unread != 1 else ''}",
                    message=f"While you were away, you received {total_unread} new message{
                        's' if total_unread != 1 else ''
                    } in {conversations_with_unread.count()} conversation{
                        's' if conversations_with_unread.count() != 1 else ''
                    }.",
                    data={
                        "unread_count": total_unread,
                        "conversation_count": conversations_with_unread.count(),
                    },
                    action_url="/messages/",
                    action_label="View Messages",
                    priority="normal",
                )


# Service instances
notification_service = NotificationService()
message_notification_service = MessageNotificationService()
