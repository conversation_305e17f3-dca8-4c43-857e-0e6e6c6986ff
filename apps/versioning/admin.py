from requests.exceptions import HTTP<PERSON>rror
import contextlib

from django.contrib import admin
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from .models import (
    UniversalVersion,
    VersionBranch,
    VersionLog,
    VersionMetrics,
    VersionPruningPolicy,
)


@admin.register(VersionLog)
class VersionLogAdmin(admin.ModelAdmin):
    """Admin interface for version logs with read-only access"""

    list_display = ("timestamp", "log_name", "model_name", "user", "action", "summary")
    list_filter = ("log_name", "model_name", "action", "timestamp")
    search_fields = ("summary", "log_name", "model_name", "user__username")
    readonly_fields = (
        "timestamp",
        "version",
        "log_name",
        "model_name",
        "action",
        "user",
        "summary",
        "metadata",
    )
    ordering = ("-timestamp",)
    date_hierarchy = "timestamp"

    def has_add_permission(self, request) -> bool:
        """Logs are auto-created, no manual addition"""
        return False

    def has_change_permission(self, request, obj=None) -> bool:
        """Logs are immutable once created"""
        return False

    def has_delete_permission(self, request, obj=None) -> bool:
        """Logs should not be deleted manually"""
        return request.user.is_superuser

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related("user", "version")


@admin.register(UniversalVersion)
class UniversalVersionAdmin(admin.ModelAdmin):
    """Admin interface for universal versions with read-only access"""

    list_display = (
        "content_type",
        "object_id",
        "version_number",
        "created_by",
        "created_at",
        "change_summary",
        "branch_name",
        "is_significant",
    )
    list_filter = ("content_type", "created_at", "branch_name", "is_significant")
    search_fields = ("change_summary", "created_by__username", "object_id")
    readonly_fields = (
        "created_at",
        "serialized_data",
        "content_type",
        "object_id",
        "version_number",
        "created_by",
        "data_size",
        "compression_ratio",
    )
    ordering = ("-created_at",)
    date_hierarchy = "created_at"

    def has_add_permission(self, request) -> bool:
        """Versions are auto-created, no manual addition"""
        return False

    def has_change_permission(self, request, obj=None) -> bool:
        """Allow editing of change summary and significance only"""
        return request.user.has_perm("versioning.change_universalversion")

    def has_delete_permission(self, request, obj=None) -> bool:
        """Only superusers can delete versions"""
        return request.user.is_superuser

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related("content_type", "created_by")

    def get_readonly_fields(self, request, obj=None):
        """Allow editing only summary and significance"""
        readonly = list(self.readonly_fields)
        if obj and request.user.has_perm("versioning.change_universalversion"):
            # Remove editable fields from readonly list
            for field in ["change_summary", "is_significant"]:
                if field in readonly:
                    readonly.remove(field)
        return readonly


@admin.register(VersionBranch)
class VersionBranchAdmin(admin.ModelAdmin):
    """Admin interface for version branches"""

    list_display = (
        "branch_name",
        "content_type",
        "object_id",
        "created_by",
        "created_at",
        "is_merged",
    )
    list_filter = ("content_type", "is_merged", "created_at")
    search_fields = ("branch_name", "description", "created_by__username")
    readonly_fields = ("created_at", "branched_from_version", "merged_into_version")
    ordering = ("-created_at",)

    def get_queryset(self, request):
        """Optimize queryset"""
        return (
            super()
            .get_queryset(request)
            .select_related(
                "content_type",
                "created_by",
                "branched_from_version",
                "merged_into_version",
            )
        )


@admin.register(VersionPruningPolicy)
class VersionPruningPolicyAdmin(admin.ModelAdmin):
    """Admin interface for version pruning policies"""

    list_display = (
        "name",
        "content_type",
        "max_versions",
        "max_age_days",
        "keep_significant_only",
        "auto_prune",
        "is_active",
    )
    list_filter = ("content_type", "auto_prune", "keep_significant_only", "is_active")
    search_fields = ("name", "description")
    ordering = ("name",)

    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related("content_type")


@admin.register(VersionMetrics)
class VersionMetricsAdmin(admin.ModelAdmin):
    """Admin interface for version metrics (read-only)"""

    list_display = (
        "content_type",
        "total_versions",
        "total_storage_mb",
        "avg_compression_ratio",
        "last_updated",
    )
    list_filter = ("content_type", "last_updated")
    readonly_fields = (
        "content_type",
        "total_versions",
        "total_storage_mb",
        "avg_compression_ratio",
        "largest_version_mb",
        "oldest_version_date",
        "last_updated",
    )
    ordering = ("-total_storage_mb",)

    def has_add_permission(self, request) -> bool:
        """Metrics are auto-generated"""
        return False

    def has_change_permission(self, request, obj=None) -> bool:
        """Metrics are auto-updated"""
        return False

    def has_delete_permission(self, request, obj=None) -> bool:
        """Metrics should not be deleted"""
        return False

    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related("content_type")


class VersioningMixin:
    """Mixin to add versioning capabilities to admin interfaces"""

    def get_history_widget(self, obj) -> str:
        """Generate history widget HTML for the object"""
        if not obj or not hasattr(obj, "get_versions"):
            return mark_safe('<p class="text-muted">No version history available</p>')

        try:
            versions = obj.get_versions()[:5]  # Latest 5 versions

            if not versions:
                return mark_safe('<p class="text-muted">No versions found</p>')

            html = '<div class="version-history-widget">'
            html += "<h4>Recent Versions</h4>"
            html += '<ul class="list-group list-group-flush">'

            for version in versions:
                html += f"""
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>v{version.version_number}</strong>
                            <small class="text-muted d-block">{version.change_summary or "No summary"}</small>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">{version.created_at.strftime("%b %d, %Y")}</small>
                            <br>
                            <small class="text-muted">by {version.created_by.username if version.created_by else "System"}</small>
                        </div>
                    </li>
                """

            html += "</ul>"

            # Add links to full history
            content_type = obj._meta.app_label
            model_name = obj._meta.model_name
            object_id = obj.pk

            try:
                history_url = reverse(
                    "versioning:universal_history",
                    kwargs={
                        "app_label": content_type,
                        "model_name": model_name,
                        "object_id": object_id,
                    },
                )
                html += '<div class="mt-2">'
                html += f'<a href="{history_url}" class="btn btn-sm btn-outline-primary">View Full History</a>'
                html += "</div>"
            except (AttributeError, KeyError, ValueError, TypeError):
                pass  # URL might not be available

            html += "</div>"
            return mark_safe(html)

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            return mark_safe(f'<p class="text-danger">Error loading version history: {e!s}</p>')

    def get_activity_widget(self, obj) -> str:
        """Generate activity widget HTML for the object"""
        if not obj:
            return mark_safe('<p class="text-muted">No activity available</p>')

        try:
            from .services import VersionLogService

            log_service = VersionLogService()
            logs = log_service.get_logs_by_object(obj, limit=5)

            if not logs:
                return mark_safe('<p class="text-muted">No recent activity</p>')

            html = '<div class="activity-widget">'
            html += "<h4>Recent Activity</h4>"
            html += '<div class="list-group">'

            for log in logs:
                action_class = {
                    "created": "success",
                    "updated": "warning",
                    "deleted": "danger",
                    "restored": "info",
                }.get(log.action, "secondary")

                html += f"""
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between">
                            <strong>{log.summary}</strong>
                            <span class="badge bg-{action_class}">{log.action}</span>
                        </div>
                        <small class="text-muted">
                            {log.timestamp.strftime("%b %d, %Y at %H:%M")} by {log.user.username if log.user else "System"}
                        </small>
                    </div>
                """

            html += "</div>"
            html += "</div>"
            return mark_safe(html)

        except (ConnectionError, TimeoutError, HTTPError) as e:
            return mark_safe(f'<p class="text-danger">Error loading activity: {e!s}</p>')

    def change_view(self, request, object_id, form_url="", extra_context=None):
        """Add versioning widgets to change view"""
        extra_context = extra_context or {}

        try:
            # Get the object
            obj = self.get_object(request, object_id)

            extra_context.update(
                {
                    "history_widget": self.get_history_widget(obj),
                    "activity_widget": self.get_activity_widget(obj),
                },
            )

            # Add URLs if they exist
            with contextlib.suppress(Exception):
                extra_context["system_history_url"] = reverse("versioning:system_history")

            with contextlib.suppress(Exception):
                extra_context["recent_changes_url"] = reverse("versioning:recent_changes_dashboard")

        except (ConnectionError, TimeoutError, HTTPError):
            pass  # Continue without widgets if there's an error

        return super().change_view(request, object_id, form_url, extra_context)


class VersioningAdminSite(admin.AdminSite):
    """Custom admin site with integrated versioning features"""

    site_header = _("CLEAR Administration")
    site_title = _("CLEAR Admin")
    index_title = _("System Administration")

    def index(self, request, extra_context=None):
        """Custom admin index with versioning statistics"""
        extra_context = extra_context or {}

        try:
            from .services import VersionLogService

            # Get recent activity summary
            log_service = VersionLogService()
            activity_summary = log_service.get_activity_summary(days=7)

            # Get recent logs
            recent_logs = VersionLog.objects.select_related("user", "version").order_by("-timestamp")[:10]

            extra_context.update(
                {
                    "activity_summary": activity_summary,
                    "recent_logs": recent_logs,
                },
            )

            # Add URLs if they exist
            try:
                extra_context["system_history_url"] = reverse("versioning:system_history")
                extra_context["recent_changes_url"] = reverse("versioning:recent_changes_dashboard")
                extra_context["audit_trail_url"] = reverse("versioning:audit_trail_report")
            except (AttributeError, KeyError, ValueError, TypeError):
                pass  # URLs might not be available

        except (ConnectionError, TimeoutError, HTTPError):
            pass  # Continue without versioning stats if there's an error

        return super().index(request, extra_context)


def add_versioning_to_admin(admin_class):
    """Helper function to add versioning mixin to existing admin classes"""
    if not issubclass(admin_class, VersioningMixin):
        # Create new class with VersioningMixin
        class VersionedAdmin(VersioningMixin, admin_class):
            pass

        # Copy class name and module for better debugging
        VersionedAdmin.__name__ = f"Versioned{admin_class.__name__}"
        VersionedAdmin.__module__ = admin_class.__module__

        return VersionedAdmin

    return admin_class
