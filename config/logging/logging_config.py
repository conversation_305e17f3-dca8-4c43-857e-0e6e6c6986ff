"""
Logging Configuration for CLEAR Application

This module provides comprehensive logging configuration with structured JSON
formatting, log rotation, and centralized aggregation support.
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# Logging configuration
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'json': {
            '()': 'apps.core.logging.JSONFormatter',
        },
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
        'django.server': {
            '()': 'django.utils.log.ServerFormatter',
            'format': '[{server_time}] {message}',
            'style': '{',
        },
    },
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse',
        },
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'json',
        },
        'console_debug': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
            'filters': ['require_debug_true'],
        },
        'file_general': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'clear.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 10,
            'formatter': 'json',
        },
        'file_error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'clear_error.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 10,
            'formatter': 'json',
        },
        'file_security': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'security.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 20,  # Keep more security logs
            'formatter': 'json',
        },
        'file_business': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'business.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 15,
            'formatter': 'json',
        },
        'file_audit': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'audit.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 30,  # Keep audit logs longer
            'formatter': 'json',
        },
        'file_performance': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'performance.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 10,
            'formatter': 'json',
        },
        'mail_admins': {
            'level': 'ERROR',
            'filters': ['require_debug_false'],
            'class': 'django.utils.log.AdminEmailHandler',
            'formatter': 'verbose',
        },
        'django.server': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'django.server',
        },
    },
    'root': {
        'level': 'INFO',
        'handlers': ['console', 'file_general'],
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file_general', 'mail_admins'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.server': {
            'handlers': ['django.server'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['file_error', 'mail_admins'],
            'level': 'ERROR',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['console_debug'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'clear': {
            'handlers': ['console', 'file_general'],
            'level': 'INFO',
            'propagate': False,
        },
        'clear.security': {
            'handlers': ['console', 'file_security', 'mail_admins'],
            'level': 'INFO',
            'propagate': False,
        },
        'clear.business': {
            'handlers': ['console', 'file_business'],
            'level': 'INFO',
            'propagate': False,
        },
        'clear.audit': {
            'handlers': ['file_audit'],
            'level': 'INFO',
            'propagate': False,
        },
        'clear.performance': {
            'handlers': ['console_debug', 'file_performance'],
            'level': 'INFO',
            'propagate': False,
        },
        'clear.requests': {
            'handlers': ['console_debug', 'file_general'],
            'level': 'INFO',
            'propagate': False,
        },
        'clear.exceptions': {
            'handlers': ['console', 'file_error', 'mail_admins'],
            'level': 'ERROR',
            'propagate': False,
        },
    },
}


def get_logging_config(environment='development'):
    """
    Get logging configuration for specific environment.
    
    Args:
        environment: 'development', 'production', 'testing'
    
    Returns:
        Logging configuration dictionary
    """
    config = LOGGING_CONFIG.copy()
    
    if environment == 'production':
        # Production-specific adjustments
        config['handlers']['console']['level'] = 'WARNING'
        config['loggers']['django']['level'] = 'WARNING'
        config['loggers']['clear']['level'] = 'INFO'
        
        # Add syslog handler for production
        config['handlers']['syslog'] = {
            'level': 'INFO',
            'class': 'logging.handlers.SysLogHandler',
            'formatter': 'json',
            'address': '/dev/log',
        }
        
        # Add syslog to root and main loggers
        config['root']['handlers'].append('syslog')
        config['loggers']['clear']['handlers'].append('syslog')
        
    elif environment == 'testing':
        # Testing-specific adjustments
        config['handlers']['console']['level'] = 'CRITICAL'
        config['loggers']['django']['level'] = 'CRITICAL'
        config['loggers']['clear']['level'] = 'CRITICAL'
        
        # Disable file logging in tests
        for logger_config in config['loggers'].values():
            logger_config['handlers'] = [
                h for h in logger_config['handlers'] 
                if not h.startswith('file_')
            ]
    
    elif environment == 'development':
        # Development-specific adjustments
        config['handlers']['console']['level'] = 'DEBUG'
        config['handlers']['console']['formatter'] = 'verbose'
        config['loggers']['django']['level'] = 'INFO'
        config['loggers']['clear']['level'] = 'DEBUG'
        
        # Add debug console handler
        for logger_config in config['loggers'].values():
            if 'console' in logger_config['handlers']:
                logger_config['handlers'].append('console_debug')
    
    return config


def setup_log_directories():
    """Create log directories if they don't exist."""
    log_dir = BASE_DIR / 'logs'
    log_dir.mkdir(exist_ok=True)
    
    # Create individual log files to ensure proper permissions
    log_files = [
        'clear.log',
        'clear_error.log',
        'security.log',
        'business.log',
        'audit.log',
        'performance.log',
    ]
    
    for log_file in log_files:
        log_path = log_dir / log_file
        if not log_path.exists():
            log_path.touch()


# ELK Stack / Centralized Logging Configuration
ELASTICSEARCH_CONFIG = {
    'hosts': [
        {'host': 'localhost', 'port': 9200}
    ],
    'index_prefix': 'clear-logs',
    'doc_type': 'log',
    'timeout': 30,
}

LOGSTASH_CONFIG = {
    'host': 'localhost',
    'port': 5000,
    'version': 1,
}

# Fluentd configuration for centralized logging
FLUENTD_CONFIG = {
    'host': 'localhost',
    'port': 24224,
    'tag': 'clear.logs',
}


def get_centralized_logging_handler(system='elasticsearch'):
    """
    Get centralized logging handler configuration.
    
    Args:
        system: 'elasticsearch', 'logstash', 'fluentd'
    
    Returns:
        Handler configuration dictionary
    """
    if system == 'elasticsearch':
        return {
            'level': 'INFO',
            'class': 'elasticsearch_logging.ElasticsearchHandler',
            'formatter': 'json',
            'hosts': ELASTICSEARCH_CONFIG['hosts'],
            'index': ELASTICSEARCH_CONFIG['index_prefix'],
            'doc_type': ELASTICSEARCH_CONFIG['doc_type'],
        }
    
    elif system == 'logstash':
        return {
            'level': 'INFO',
            'class': 'logstash_async.handler.AsynchronousLogstashHandler',
            'formatter': 'json',
            'host': LOGSTASH_CONFIG['host'],
            'port': LOGSTASH_CONFIG['port'],
            'version': LOGSTASH_CONFIG['version'],
        }
    
    elif system == 'fluentd':
        return {
            'level': 'INFO',
            'class': 'fluent.handler.FluentHandler',
            'formatter': 'json',
            'host': FLUENTD_CONFIG['host'],
            'port': FLUENTD_CONFIG['port'],
            'tag': FLUENTD_CONFIG['tag'],
        }
    
    else:
        raise ValueError(f"Unsupported centralized logging system: {system}")


# Log retention policies
LOG_RETENTION_POLICIES = {
    'general': {
        'days': 30,
        'max_size_mb': 500,
    },
    'error': {
        'days': 90,
        'max_size_mb': 500,
    },
    'security': {
        'days': 365,  # Keep security logs for 1 year
        'max_size_mb': 1000,
    },
    'business': {
        'days': 180,  # Keep business logs for 6 months
        'max_size_mb': 1000,
    },
    'audit': {
        'days': 2555,  # Keep audit logs for 7 years (compliance)
        'max_size_mb': 2000,
    },
    'performance': {
        'days': 30,
        'max_size_mb': 500,
    },
}
