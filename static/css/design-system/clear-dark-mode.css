/**
 * CLEAR Dark Mode System
 * Comprehensive dark theme implementation for the CLEAR Infrastructure Platform
 *
 * Created: 2025-07-28
 * Task: #47 - Implement dark mode support throughout the application
 * Framework: Bootstrap 5.3.0 + CLEAR Design System + CSS Custom Properties
 */

/* ==========================================================================
   Dark Mode Foundation
   ========================================================================== */

/* Light theme (default) - inherits from clear-design-system.css */
:root {
  --clear-theme: 'light';

  /* Theme-aware semantic colors for better dark mode support */
  --clear-bg-primary: var(--clear-white);
  --clear-bg-secondary: var(--clear-gray-50);
  --clear-bg-tertiary: var(--clear-gray-100);
  --clear-bg-elevated: var(--clear-white);

  --clear-text-primary: var(--clear-gray-900);
  --clear-text-secondary: var(--clear-gray-700);
  --clear-text-tertiary: var(--clear-gray-500);
  --clear-text-inverse: var(--clear-white);

  --clear-border-primary: var(--clear-gray-200);
  --clear-border-secondary: var(--clear-gray-300);
  --clear-border-focus: var(--clear-primary);

  /* Interactive states */
  --clear-hover-bg: var(--clear-gray-50);
  --clear-active-bg: var(--clear-gray-100);
  --clear-focus-ring: 0 0 0 3px rgba(140, 198, 63, 0.1);

  /* Component-specific variables */
  --clear-card-bg: var(--clear-white);
  --clear-card-border: var(--clear-gray-200);
  --clear-input-bg: var(--clear-white);
  --clear-input-border: var(--clear-gray-300);
  --clear-navbar-bg: var(--clear-white);
  --clear-sidebar-bg: var(--clear-gray-50);
  --clear-modal-bg: var(--clear-white);
  --clear-dropdown-bg: var(--clear-white);
  --clear-table-stripe: var(--clear-gray-50);
}

/* Dark theme variables */
[data-theme="dark"] {
  --clear-theme: 'dark';

  /* Dark mode color palette */
  --clear-dark-900: #0a0a0a;
  --clear-dark-800: #1a1a1a;
  --clear-dark-700: #2a2a2a;
  --clear-dark-600: #3a3a3a;
  --clear-dark-500: #4a4a4a;
  --clear-dark-400: #6a6a6a;
  --clear-dark-300: #8a8a8a;
  --clear-dark-200: #aaaaaa;
  --clear-dark-100: #cccccc;
  --clear-dark-50: #e5e5e5;

  /* Override semantic colors for dark mode */
  --clear-bg-primary: var(--clear-dark-900);
  --clear-bg-secondary: var(--clear-dark-800);
  --clear-bg-tertiary: var(--clear-dark-700);
  --clear-bg-elevated: var(--clear-dark-800);

  --clear-text-primary: var(--clear-dark-50);
  --clear-text-secondary: var(--clear-dark-200);
  --clear-text-tertiary: var(--clear-dark-300);
  --clear-text-inverse: var(--clear-dark-900);

  --clear-border-primary: var(--clear-dark-600);
  --clear-border-secondary: var(--clear-dark-500);
  --clear-border-focus: var(--clear-primary-light);

  /* Interactive states for dark mode */
  --clear-hover-bg: var(--clear-dark-700);
  --clear-active-bg: var(--clear-dark-600);
  --clear-focus-ring: 0 0 0 3px rgba(164, 214, 94, 0.2);

  /* Component-specific dark mode variables */
  --clear-card-bg: var(--clear-dark-800);
  --clear-card-border: var(--clear-dark-600);
  --clear-input-bg: var(--clear-dark-700);
  --clear-input-border: var(--clear-dark-500);
  --clear-navbar-bg: var(--clear-dark-800);
  --clear-sidebar-bg: var(--clear-dark-900);
  --clear-modal-bg: var(--clear-dark-800);
  --clear-dropdown-bg: var(--clear-dark-800);
  --clear-table-stripe: var(--clear-dark-700);

  /* Adjust shadows for dark mode */
  --clear-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --clear-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --clear-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --clear-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --clear-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  --clear-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);

  /* Adjust brand colors for better dark mode contrast */
  --clear-primary: #A4D65E;
  --clear-primary-dark: #8CC63F;
  --clear-primary-light: #B8E076;

  /* Adjust semantic colors for dark mode */
  --clear-success: #2ECC71;
  --clear-warning: #F1C40F;
  --clear-error: #E67E22;
  --clear-info: #5DADE2;
}

/* ==========================================================================
   System Preference Detection
   ========================================================================== */

/* Respect system preference when no explicit theme is set */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    --clear-theme: 'dark';

    /* Apply dark mode variables when system preference is dark */
    --clear-dark-900: #0a0a0a;
    --clear-dark-800: #1a1a1a;
    --clear-dark-700: #2a2a2a;
    --clear-dark-600: #3a3a3a;
    --clear-dark-500: #4a4a4a;
    --clear-dark-400: #6a6a6a;
    --clear-dark-300: #8a8a8a;
    --clear-dark-200: #aaaaaa;
    --clear-dark-100: #cccccc;
    --clear-dark-50: #e5e5e5;

    --clear-bg-primary: var(--clear-dark-900);
    --clear-bg-secondary: var(--clear-dark-800);
    --clear-bg-tertiary: var(--clear-dark-700);
    --clear-bg-elevated: var(--clear-dark-800);

    --clear-text-primary: var(--clear-dark-50);
    --clear-text-secondary: var(--clear-dark-200);
    --clear-text-tertiary: var(--clear-dark-300);
    --clear-text-inverse: var(--clear-dark-900);

    --clear-border-primary: var(--clear-dark-600);
    --clear-border-secondary: var(--clear-dark-500);
    --clear-border-focus: var(--clear-primary-light);

    --clear-hover-bg: var(--clear-dark-700);
    --clear-active-bg: var(--clear-dark-600);
    --clear-focus-ring: 0 0 0 3px rgba(164, 214, 94, 0.2);

    --clear-card-bg: var(--clear-dark-800);
    --clear-card-border: var(--clear-dark-600);
    --clear-input-bg: var(--clear-dark-700);
    --clear-input-border: var(--clear-dark-500);
    --clear-navbar-bg: var(--clear-dark-800);
    --clear-sidebar-bg: var(--clear-dark-900);
    --clear-modal-bg: var(--clear-dark-800);
    --clear-dropdown-bg: var(--clear-dark-800);
    --clear-table-stripe: var(--clear-dark-700);

    --clear-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --clear-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --clear-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --clear-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --clear-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
    --clear-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);

    --clear-primary: #A4D65E;
    --clear-primary-dark: #8CC63F;
    --clear-primary-light: #B8E076;

    --clear-success: #2ECC71;
    --clear-warning: #F1C40F;
    --clear-error: #E67E22;
    --clear-info: #5DADE2;
  }
}

/* ==========================================================================
   Base Element Styling
   ========================================================================== */

/* Apply theme-aware colors to base elements */
body {
  background-color: var(--clear-bg-primary);
  color: var(--clear-text-primary);
  transition: background-color var(--clear-transition-base), color var(--clear-transition-base);
}

/* ==========================================================================
   Component Overrides for Dark Mode
   ========================================================================== */

/* Cards */
.clear-card,
.card {
  background-color: var(--clear-card-bg);
  border-color: var(--clear-card-border);
  color: var(--clear-text-primary);
  transition: background-color var(--clear-transition-base), border-color var(--clear-transition-base);
}

.clear-card-header,
.card-header {
  background-color: var(--clear-bg-secondary);
  border-bottom-color: var(--clear-border-primary);
}

.clear-card-footer,
.card-footer {
  background-color: var(--clear-bg-secondary);
  border-top-color: var(--clear-border-primary);
}

/* Forms */
.clear-input,
.form-control,
.form-select {
  background-color: var(--clear-input-bg);
  border-color: var(--clear-input-border);
  color: var(--clear-text-primary);
  transition: background-color var(--clear-transition-base), border-color var(--clear-transition-base);
}

.clear-input:focus,
.form-control:focus,
.form-select:focus {
  background-color: var(--clear-input-bg);
  border-color: var(--clear-border-focus);
  box-shadow: var(--clear-focus-ring);
  color: var(--clear-text-primary);
}

.clear-input::placeholder,
.form-control::placeholder {
  color: var(--clear-text-tertiary);
}

/* Labels */
.clear-label,
.form-label {
  color: var(--clear-text-secondary);
}

/* Navigation */
.clear-navbar,
.navbar {
  background-color: var(--clear-navbar-bg);
  border-bottom-color: var(--clear-border-primary);
}

.clear-nav-link,
.navbar-nav .nav-link {
  color: var(--clear-text-secondary);
  transition: color var(--clear-transition-fast);
}

.clear-nav-link:hover,
.navbar-nav .nav-link:hover {
  color: var(--clear-text-primary);
}

.clear-nav-link.active,
.navbar-nav .nav-link.active {
  color: var(--clear-primary);
}

/* Sidebar */
.clear-sidebar {
  background-color: var(--clear-sidebar-bg);
  border-right-color: var(--clear-border-primary);
}

.clear-sidebar-link {
  color: var(--clear-text-secondary);
  transition: background-color var(--clear-transition-fast), color var(--clear-transition-fast);
}

.clear-sidebar-link:hover {
  background-color: var(--clear-hover-bg);
  color: var(--clear-text-primary);
}

.clear-sidebar-link.active {
  background-color: var(--clear-primary);
  color: var(--clear-text-inverse);
}

/* Buttons */
.clear-btn-secondary,
.btn-secondary {
  background-color: var(--clear-bg-secondary);
  border-color: var(--clear-border-primary);
  color: var(--clear-text-primary);
}

.clear-btn-secondary:hover,
.btn-secondary:hover {
  background-color: var(--clear-hover-bg);
  border-color: var(--clear-border-secondary);
  color: var(--clear-text-primary);
}

.clear-btn-outline-secondary,
.btn-outline-secondary {
  border-color: var(--clear-border-primary);
  color: var(--clear-text-primary);
}

.clear-btn-outline-secondary:hover,
.btn-outline-secondary:hover {
  background-color: var(--clear-hover-bg);
  border-color: var(--clear-border-secondary);
  color: var(--clear-text-primary);
}

/* Tables */
.clear-table,
.table {
  color: var(--clear-text-primary);
}

.clear-table th,
.table th {
  background-color: var(--clear-bg-secondary);
  border-bottom-color: var(--clear-border-primary);
  color: var(--clear-text-secondary);
}

.clear-table td,
.table td {
  border-bottom-color: var(--clear-border-primary);
}

.clear-table-striped tbody tr:nth-of-type(odd),
.table-striped tbody tr:nth-of-type(odd) {
  background-color: var(--clear-table-stripe);
}

.clear-table-hover tbody tr:hover,
.table-hover tbody tr:hover {
  background-color: var(--clear-hover-bg);
}

/* Modals */
.clear-modal,
.modal-content {
  background-color: var(--clear-modal-bg);
  color: var(--clear-text-primary);
}

.clear-modal-header,
.modal-header {
  border-bottom-color: var(--clear-border-primary);
}

.clear-modal-footer,
.modal-footer {
  border-top-color: var(--clear-border-primary);
}

/* Dropdowns */
.clear-dropdown-menu,
.dropdown-menu {
  background-color: var(--clear-dropdown-bg);
  border-color: var(--clear-border-primary);
  box-shadow: var(--clear-shadow-lg);
}

.clear-dropdown-item,
.dropdown-item {
  color: var(--clear-text-primary);
  transition: background-color var(--clear-transition-fast);
}

.clear-dropdown-item:hover,
.dropdown-item:hover {
  background-color: var(--clear-hover-bg);
  color: var(--clear-text-primary);
}

.clear-dropdown-divider,
.dropdown-divider {
  border-top-color: var(--clear-border-primary);
}

/* Alerts */
.clear-alert,
.alert {
  border-color: var(--clear-border-primary);
}

/* List Groups */
.clear-list-group,
.list-group {
  background-color: transparent;
}

.clear-list-group-item,
.list-group-item {
  background-color: var(--clear-bg-primary);
  border-color: var(--clear-border-primary);
  color: var(--clear-text-primary);
}

.clear-list-group-item:hover,
.list-group-item:hover {
  background-color: var(--clear-hover-bg);
}

.clear-list-group-item.active,
.list-group-item.active {
  background-color: var(--clear-primary);
  border-color: var(--clear-primary);
  color: var(--clear-text-inverse);
}

/* Badges */
.clear-badge-secondary,
.badge.bg-secondary {
  background-color: var(--clear-bg-secondary);
  color: var(--clear-text-primary);
}

/* Progress bars */
.clear-progress,
.progress {
  background-color: var(--clear-bg-secondary);
}

/* Breadcrumbs */
.clear-breadcrumb,
.breadcrumb {
  background-color: var(--clear-bg-secondary);
}

.clear-breadcrumb-item,
.breadcrumb-item {
  color: var(--clear-text-secondary);
}

.clear-breadcrumb-item.active,
.breadcrumb-item.active {
  color: var(--clear-text-primary);
}

/* Pagination */
.clear-page-link,
.page-link {
  background-color: var(--clear-bg-primary);
  border-color: var(--clear-border-primary);
  color: var(--clear-text-primary);
}

.clear-page-link:hover,
.page-link:hover {
  background-color: var(--clear-hover-bg);
  border-color: var(--clear-border-secondary);
  color: var(--clear-text-primary);
}

.clear-page-item.active .clear-page-link,
.page-item.active .page-link {
  background-color: var(--clear-primary);
  border-color: var(--clear-primary);
  color: var(--clear-text-inverse);
}

/* ==========================================================================
   Theme Toggle Component
   ========================================================================== */

.clear-theme-toggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: var(--clear-space-2) var(--clear-space-3);
  background-color: var(--clear-bg-secondary);
  border: 1px solid var(--clear-border-primary);
  border-radius: var(--clear-radius-lg);
  cursor: pointer;
  transition: all var(--clear-transition-base);
  user-select: none;
}

.clear-theme-toggle:hover {
  background-color: var(--clear-hover-bg);
  border-color: var(--clear-border-secondary);
}

.clear-theme-toggle-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: var(--clear-space-2);
  transition: transform var(--clear-transition-base);
}

.clear-theme-toggle-text {
  font-size: var(--clear-font-size-sm);
  font-weight: var(--clear-font-weight-medium);
  color: var(--clear-text-secondary);
  transition: color var(--clear-transition-base);
}

/* Theme toggle states */
[data-theme="dark"] .clear-theme-toggle-icon {
  transform: rotate(180deg);
}

.clear-theme-toggle:hover .clear-theme-toggle-text {
  color: var(--clear-text-primary);
}

/* Theme toggle button variant */
.clear-theme-toggle-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  background-color: var(--clear-bg-secondary);
  border: 1px solid var(--clear-border-primary);
  border-radius: var(--clear-radius-full);
  cursor: pointer;
  transition: all var(--clear-transition-base);
}

.clear-theme-toggle-btn:hover {
  background-color: var(--clear-hover-bg);
  border-color: var(--clear-border-secondary);
  transform: scale(1.05);
}

.clear-theme-toggle-btn:active {
  transform: scale(0.95);
}

.clear-theme-toggle-btn .clear-theme-toggle-icon {
  margin: 0;
}

/* ==========================================================================
   Accessibility & Reduced Motion
   ========================================================================== */

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .clear-theme-toggle,
  .clear-theme-toggle-btn,
  .clear-theme-toggle-icon {
    transition: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --clear-border-primary: currentColor;
    --clear-border-secondary: currentColor;
  }

  [data-theme="dark"] {
    --clear-text-primary: #ffffff;
    --clear-text-secondary: #e0e0e0;
    --clear-bg-primary: #000000;
    --clear-bg-secondary: #1a1a1a;
  }
}

/* Focus indicators for keyboard navigation */
.clear-theme-toggle:focus-visible,
.clear-theme-toggle-btn:focus-visible {
  outline: 2px solid var(--clear-primary);
  outline-offset: 2px;
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
  /* Force light theme for printing */
  * {
    background-color: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  .clear-theme-toggle,
  .clear-theme-toggle-btn {
    display: none !important;
  }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

/* Theme-aware utility classes */
.clear-bg-primary { background-color: var(--clear-bg-primary); }
.clear-bg-secondary { background-color: var(--clear-bg-secondary); }
.clear-bg-tertiary { background-color: var(--clear-bg-tertiary); }
.clear-bg-elevated { background-color: var(--clear-bg-elevated); }

.clear-text-primary { color: var(--clear-text-primary); }
.clear-text-secondary { color: var(--clear-text-secondary); }
.clear-text-tertiary { color: var(--clear-text-tertiary); }
.clear-text-inverse { color: var(--clear-text-inverse); }

.clear-border-primary { border-color: var(--clear-border-primary); }
.clear-border-secondary { border-color: var(--clear-border-secondary); }

/* Theme detection utilities */
.clear-light-only {
  display: block;
}

.clear-dark-only {
  display: none;
}

[data-theme="dark"] .clear-light-only {
  display: none;
}

[data-theme="dark"] .clear-dark-only {
  display: block;
}

@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) .clear-light-only {
    display: none;
  }

  :root:not([data-theme]) .clear-dark-only {
    display: block;
  }
}
