/**
 * CLEAR Design System - Comprehensive Component Library
 * Consistent design patterns and components for the CLEAR Infrastructure Platform
 */

/* ==========================================================================
   Design System Foundation
   ========================================================================== */

:root {
  /* Brand Colors */
  --clear-primary: #8CC63F;
  --clear-primary-dark: #7AB82F;
  --clear-primary-light: #A4D65E;
  --clear-secondary: #2C3E50;
  --clear-accent: #3498DB;

  /* Semantic Colors */
  --clear-success: #27AE60;
  --clear-warning: #F39C12;
  --clear-error: #E74C3C;
  --clear-info: #3498DB;

  /* Neutral Colors */
  --clear-white: #FFFFFF;
  --clear-gray-50: #F8F9FA;
  --clear-gray-100: #E9ECEF;
  --clear-gray-200: #DEE2E6;
  --clear-gray-300: #CED4DA;
  --clear-gray-400: #ADB5BD;
  --clear-gray-500: #6C757D;
  --clear-gray-600: #495057;
  --clear-gray-700: #343A40;
  --clear-gray-800: #212529;
  --clear-gray-900: #000000;

  /* Typography Scale */
  --clear-font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --clear-font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;

  --clear-font-size-xs: 0.75rem;    /* 12px */
  --clear-font-size-sm: 0.875rem;   /* 14px */
  --clear-font-size-base: 1rem;     /* 16px */
  --clear-font-size-lg: 1.125rem;   /* 18px */
  --clear-font-size-xl: 1.25rem;    /* 20px */
  --clear-font-size-2xl: 1.5rem;    /* 24px */
  --clear-font-size-3xl: 1.875rem;  /* 30px */
  --clear-font-size-4xl: 2.25rem;   /* 36px */
  --clear-font-size-5xl: 3rem;      /* 48px */

  --clear-font-weight-light: 300;
  --clear-font-weight-normal: 400;
  --clear-font-weight-medium: 500;
  --clear-font-weight-semibold: 600;
  --clear-font-weight-bold: 700;

  --clear-line-height-tight: 1.25;
  --clear-line-height-normal: 1.5;
  --clear-line-height-relaxed: 1.75;

  /* Spacing Scale */
  --clear-space-0: 0;
  --clear-space-1: 0.25rem;   /* 4px */
  --clear-space-2: 0.5rem;    /* 8px */
  --clear-space-3: 0.75rem;   /* 12px */
  --clear-space-4: 1rem;      /* 16px */
  --clear-space-5: 1.25rem;   /* 20px */
  --clear-space-6: 1.5rem;    /* 24px */
  --clear-space-8: 2rem;      /* 32px */
  --clear-space-10: 2.5rem;   /* 40px */
  --clear-space-12: 3rem;     /* 48px */
  --clear-space-16: 4rem;     /* 64px */
  --clear-space-20: 5rem;     /* 80px */
  --clear-space-24: 6rem;     /* 96px */

  /* Border Radius */
  --clear-radius-none: 0;
  --clear-radius-sm: 0.125rem;   /* 2px */
  --clear-radius-base: 0.25rem;  /* 4px */
  --clear-radius-md: 0.375rem;   /* 6px */
  --clear-radius-lg: 0.5rem;     /* 8px */
  --clear-radius-xl: 0.75rem;    /* 12px */
  --clear-radius-2xl: 1rem;      /* 16px */
  --clear-radius-full: 9999px;

  /* Shadows */
  --clear-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --clear-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --clear-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --clear-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --clear-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --clear-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Transitions */
  --clear-transition-fast: 150ms ease-in-out;
  --clear-transition-base: 250ms ease-in-out;
  --clear-transition-slow: 350ms ease-in-out;

  /* Z-Index Scale */
  --clear-z-dropdown: 1000;
  --clear-z-sticky: 1020;
  --clear-z-fixed: 1030;
  --clear-z-modal-backdrop: 1040;
  --clear-z-modal: 1050;
  --clear-z-popover: 1060;
  --clear-z-tooltip: 1070;
  --clear-z-toast: 1080;
}

/* ==========================================================================
   Base Typography
   ========================================================================== */

.clear-text-xs { font-size: var(--clear-font-size-xs); }
.clear-text-sm { font-size: var(--clear-font-size-sm); }
.clear-text-base { font-size: var(--clear-font-size-base); }
.clear-text-lg { font-size: var(--clear-font-size-lg); }
.clear-text-xl { font-size: var(--clear-font-size-xl); }
.clear-text-2xl { font-size: var(--clear-font-size-2xl); }
.clear-text-3xl { font-size: var(--clear-font-size-3xl); }
.clear-text-4xl { font-size: var(--clear-font-size-4xl); }
.clear-text-5xl { font-size: var(--clear-font-size-5xl); }

.clear-font-light { font-weight: var(--clear-font-weight-light); }
.clear-font-normal { font-weight: var(--clear-font-weight-normal); }
.clear-font-medium { font-weight: var(--clear-font-weight-medium); }
.clear-font-semibold { font-weight: var(--clear-font-weight-semibold); }
.clear-font-bold { font-weight: var(--clear-font-weight-bold); }

.clear-leading-tight { line-height: var(--clear-line-height-tight); }
.clear-leading-normal { line-height: var(--clear-line-height-normal); }
.clear-leading-relaxed { line-height: var(--clear-line-height-relaxed); }

/* ==========================================================================
   Button Components
   ========================================================================== */

.clear-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--clear-space-2) var(--clear-space-4);
  font-family: var(--clear-font-family-primary);
  font-size: var(--clear-font-size-sm);
  font-weight: var(--clear-font-weight-medium);
  line-height: var(--clear-line-height-tight);
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--clear-radius-md);
  cursor: pointer;
  transition: all var(--clear-transition-fast);
  user-select: none;
  white-space: nowrap;
}

.clear-btn:focus {
  outline: 2px solid var(--clear-primary);
  outline-offset: 2px;
}

.clear-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Variants */
.clear-btn-primary {
  background-color: var(--clear-primary);
  border-color: var(--clear-primary);
  color: var(--clear-white);
}

.clear-btn-primary:hover:not(:disabled) {
  background-color: var(--clear-primary-dark);
  border-color: var(--clear-primary-dark);
}

.clear-btn-secondary {
  background-color: var(--clear-secondary);
  border-color: var(--clear-secondary);
  color: var(--clear-white);
}

.clear-btn-secondary:hover:not(:disabled) {
  background-color: var(--clear-gray-700);
  border-color: var(--clear-gray-700);
}

.clear-btn-outline {
  background-color: transparent;
  border-color: var(--clear-primary);
  color: var(--clear-primary);
}

.clear-btn-outline:hover:not(:disabled) {
  background-color: var(--clear-primary);
  color: var(--clear-white);
}

.clear-btn-ghost {
  background-color: transparent;
  border-color: transparent;
  color: var(--clear-gray-600);
}

.clear-btn-ghost:hover:not(:disabled) {
  background-color: var(--clear-gray-100);
  color: var(--clear-gray-800);
}

/* Button Sizes */
.clear-btn-xs {
  padding: var(--clear-space-1) var(--clear-space-2);
  font-size: var(--clear-font-size-xs);
}

.clear-btn-sm {
  padding: var(--clear-space-2) var(--clear-space-3);
  font-size: var(--clear-font-size-sm);
}

.clear-btn-lg {
  padding: var(--clear-space-3) var(--clear-space-6);
  font-size: var(--clear-font-size-lg);
}

.clear-btn-xl {
  padding: var(--clear-space-4) var(--clear-space-8);
  font-size: var(--clear-font-size-xl);
}

/* Button States */
.clear-btn-success {
  background-color: var(--clear-success);
  border-color: var(--clear-success);
  color: var(--clear-white);
}

.clear-btn-warning {
  background-color: var(--clear-warning);
  border-color: var(--clear-warning);
  color: var(--clear-white);
}

.clear-btn-error {
  background-color: var(--clear-error);
  border-color: var(--clear-error);
  color: var(--clear-white);
}

/* ==========================================================================
   Card Components
   ========================================================================== */

.clear-card {
  background-color: var(--clear-white);
  border: 1px solid var(--clear-gray-200);
  border-radius: var(--clear-radius-lg);
  box-shadow: var(--clear-shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--clear-transition-base);
}

.clear-card:hover {
  box-shadow: var(--clear-shadow-md);
}

.clear-card-header {
  padding: var(--clear-space-4) var(--clear-space-6);
  border-bottom: 1px solid var(--clear-gray-200);
  background-color: var(--clear-gray-50);
}

.clear-card-body {
  padding: var(--clear-space-6);
}

.clear-card-footer {
  padding: var(--clear-space-4) var(--clear-space-6);
  border-top: 1px solid var(--clear-gray-200);
  background-color: var(--clear-gray-50);
}

.clear-card-title {
  margin: 0;
  font-size: var(--clear-font-size-lg);
  font-weight: var(--clear-font-weight-semibold);
  color: var(--clear-gray-800);
}

.clear-card-subtitle {
  margin: var(--clear-space-1) 0 0 0;
  font-size: var(--clear-font-size-sm);
  color: var(--clear-gray-600);
}

/* ==========================================================================
   Form Components
   ========================================================================== */

.clear-form-group {
  margin-bottom: var(--clear-space-4);
}

.clear-label {
  display: block;
  margin-bottom: var(--clear-space-2);
  font-size: var(--clear-font-size-sm);
  font-weight: var(--clear-font-weight-medium);
  color: var(--clear-gray-700);
}

.clear-label-required::after {
  content: " *";
  color: var(--clear-error);
}

.clear-input {
  display: block;
  width: 100%;
  padding: var(--clear-space-3) var(--clear-space-4);
  font-family: var(--clear-font-family-primary);
  font-size: var(--clear-font-size-base);
  line-height: var(--clear-line-height-normal);
  color: var(--clear-gray-800);
  background-color: var(--clear-white);
  border: 1px solid var(--clear-gray-300);
  border-radius: var(--clear-radius-md);
  transition: border-color var(--clear-transition-fast), box-shadow var(--clear-transition-fast);
}

.clear-input:focus {
  outline: none;
  border-color: var(--clear-primary);
  box-shadow: 0 0 0 3px rgba(140, 198, 63, 0.1);
}

.clear-input:disabled {
  background-color: var(--clear-gray-100);
  color: var(--clear-gray-500);
  cursor: not-allowed;
}

.clear-input-error {
  border-color: var(--clear-error);
}

.clear-input-error:focus {
  border-color: var(--clear-error);
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.clear-textarea {
  resize: vertical;
  min-height: 100px;
}

.clear-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--clear-space-3) center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: var(--clear-space-10);
}

.clear-help-text {
  margin-top: var(--clear-space-1);
  font-size: var(--clear-font-size-sm);
  color: var(--clear-gray-600);
}

.clear-error-text {
  margin-top: var(--clear-space-1);
  font-size: var(--clear-font-size-sm);
  color: var(--clear-error);
}

/* ==========================================================================
   Alert Components
   ========================================================================== */

.clear-alert {
  padding: var(--clear-space-4);
  border: 1px solid transparent;
  border-radius: var(--clear-radius-md);
  margin-bottom: var(--clear-space-4);
}

.clear-alert-success {
  background-color: rgba(39, 174, 96, 0.1);
  border-color: var(--clear-success);
  color: #155724;
}

.clear-alert-warning {
  background-color: rgba(243, 156, 18, 0.1);
  border-color: var(--clear-warning);
  color: #856404;
}

.clear-alert-error {
  background-color: rgba(231, 76, 60, 0.1);
  border-color: var(--clear-error);
  color: #721c24;
}

.clear-alert-info {
  background-color: rgba(52, 152, 219, 0.1);
  border-color: var(--clear-info);
  color: #0c5460;
}

.clear-alert-title {
  font-weight: var(--clear-font-weight-semibold);
  margin-bottom: var(--clear-space-2);
}

/* ==========================================================================
   Badge Components
   ========================================================================== */

.clear-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--clear-space-1) var(--clear-space-2);
  font-size: var(--clear-font-size-xs);
  font-weight: var(--clear-font-weight-medium);
  line-height: 1;
  border-radius: var(--clear-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.clear-badge-primary {
  background-color: var(--clear-primary);
  color: var(--clear-white);
}

.clear-badge-secondary {
  background-color: var(--clear-gray-500);
  color: var(--clear-white);
}

.clear-badge-success {
  background-color: var(--clear-success);
  color: var(--clear-white);
}

.clear-badge-warning {
  background-color: var(--clear-warning);
  color: var(--clear-white);
}

.clear-badge-error {
  background-color: var(--clear-error);
  color: var(--clear-white);
}

.clear-badge-outline {
  background-color: transparent;
  border: 1px solid var(--clear-gray-300);
  color: var(--clear-gray-600);
}

/* ==========================================================================
   Loading Components
   ========================================================================== */

.clear-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--clear-gray-200);
  border-top-color: var(--clear-primary);
  border-radius: 50%;
  animation: clear-spin 1s linear infinite;
}

.clear-spinner-sm {
  width: 0.75rem;
  height: 0.75rem;
  border-width: 1px;
}

.clear-spinner-lg {
  width: 1.5rem;
  height: 1.5rem;
  border-width: 3px;
}

@keyframes clear-spin {
  to {
    transform: rotate(360deg);
  }
}

.clear-skeleton {
  background: linear-gradient(90deg, var(--clear-gray-200) 25%, var(--clear-gray-100) 50%, var(--clear-gray-200) 75%);
  background-size: 200% 100%;
  animation: clear-skeleton 1.5s infinite;
  border-radius: var(--clear-radius-base);
}

@keyframes clear-skeleton {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ==========================================================================
   Navigation Components
   ========================================================================== */

.clear-nav {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  margin: 0;
  padding: 0;
}

.clear-nav-item {
  margin-right: var(--clear-space-1);
}

.clear-nav-link {
  display: block;
  padding: var(--clear-space-2) var(--clear-space-4);
  color: var(--clear-gray-600);
  text-decoration: none;
  border-radius: var(--clear-radius-md);
  transition: all var(--clear-transition-fast);
}

.clear-nav-link:hover {
  color: var(--clear-primary);
  background-color: var(--clear-gray-100);
}

.clear-nav-link.active {
  color: var(--clear-primary);
  background-color: rgba(140, 198, 63, 0.1);
  font-weight: var(--clear-font-weight-medium);
}

/* ==========================================================================
   Table Components
   ========================================================================== */

.clear-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--clear-white);
  border-radius: var(--clear-radius-lg);
  overflow: hidden;
  box-shadow: var(--clear-shadow-sm);
}

.clear-table th,
.clear-table td {
  padding: var(--clear-space-3) var(--clear-space-4);
  text-align: left;
  border-bottom: 1px solid var(--clear-gray-200);
}

.clear-table th {
  background-color: var(--clear-gray-50);
  font-weight: var(--clear-font-weight-semibold);
  color: var(--clear-gray-700);
  font-size: var(--clear-font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.clear-table tbody tr:hover {
  background-color: var(--clear-gray-50);
}

.clear-table tbody tr:last-child td {
  border-bottom: none;
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

/* Spacing */
.clear-m-0 { margin: 0; }
.clear-m-1 { margin: var(--clear-space-1); }
.clear-m-2 { margin: var(--clear-space-2); }
.clear-m-3 { margin: var(--clear-space-3); }
.clear-m-4 { margin: var(--clear-space-4); }
.clear-m-5 { margin: var(--clear-space-5); }
.clear-m-6 { margin: var(--clear-space-6); }
.clear-m-8 { margin: var(--clear-space-8); }

.clear-p-0 { padding: 0; }
.clear-p-1 { padding: var(--clear-space-1); }
.clear-p-2 { padding: var(--clear-space-2); }
.clear-p-3 { padding: var(--clear-space-3); }
.clear-p-4 { padding: var(--clear-space-4); }
.clear-p-5 { padding: var(--clear-space-5); }
.clear-p-6 { padding: var(--clear-space-6); }
.clear-p-8 { padding: var(--clear-space-8); }

/* Colors */
.clear-text-primary { color: var(--clear-primary); }
.clear-text-secondary { color: var(--clear-secondary); }
.clear-text-success { color: var(--clear-success); }
.clear-text-warning { color: var(--clear-warning); }
.clear-text-error { color: var(--clear-error); }
.clear-text-muted { color: var(--clear-gray-600); }

.clear-bg-primary { background-color: var(--clear-primary); }
.clear-bg-secondary { background-color: var(--clear-secondary); }
.clear-bg-success { background-color: var(--clear-success); }
.clear-bg-warning { background-color: var(--clear-warning); }
.clear-bg-error { background-color: var(--clear-error); }
.clear-bg-gray-50 { background-color: var(--clear-gray-50); }
.clear-bg-gray-100 { background-color: var(--clear-gray-100); }

/* Display */
.clear-block { display: block; }
.clear-inline { display: inline; }
.clear-inline-block { display: inline-block; }
.clear-flex { display: flex; }
.clear-inline-flex { display: inline-flex; }
.clear-grid { display: grid; }
.clear-hidden { display: none; }

/* Flexbox */
.clear-flex-col { flex-direction: column; }
.clear-flex-row { flex-direction: row; }
.clear-items-center { align-items: center; }
.clear-items-start { align-items: flex-start; }
.clear-items-end { align-items: flex-end; }
.clear-justify-center { justify-content: center; }
.clear-justify-between { justify-content: space-between; }
.clear-justify-start { justify-content: flex-start; }
.clear-justify-end { justify-content: flex-end; }

/* Text Alignment */
.clear-text-left { text-align: left; }
.clear-text-center { text-align: center; }
.clear-text-right { text-align: right; }

/* Border Radius */
.clear-rounded-none { border-radius: var(--clear-radius-none); }
.clear-rounded-sm { border-radius: var(--clear-radius-sm); }
.clear-rounded { border-radius: var(--clear-radius-base); }
.clear-rounded-md { border-radius: var(--clear-radius-md); }
.clear-rounded-lg { border-radius: var(--clear-radius-lg); }
.clear-rounded-xl { border-radius: var(--clear-radius-xl); }
.clear-rounded-full { border-radius: var(--clear-radius-full); }

/* Shadows */
.clear-shadow-none { box-shadow: none; }
.clear-shadow-sm { box-shadow: var(--clear-shadow-sm); }
.clear-shadow { box-shadow: var(--clear-shadow-base); }
.clear-shadow-md { box-shadow: var(--clear-shadow-md); }
.clear-shadow-lg { box-shadow: var(--clear-shadow-lg); }
.clear-shadow-xl { box-shadow: var(--clear-shadow-xl); }

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 640px) {
  .clear-btn {
    width: 100%;
    justify-content: center;
  }

  .clear-card {
    margin: var(--clear-space-2);
  }

  .clear-table {
    font-size: var(--clear-font-size-sm);
  }

  .clear-table th,
  .clear-table td {
    padding: var(--clear-space-2) var(--clear-space-3);
  }
}

/* ==========================================================================
   Dark Mode Support
   ========================================================================== */

@media (prefers-color-scheme: dark) {
  :root {
    --clear-white: #1a1a1a;
    --clear-gray-50: #2d2d2d;
    --clear-gray-100: #3a3a3a;
    --clear-gray-200: #4a4a4a;
    --clear-gray-300: #5a5a5a;
    --clear-gray-400: #6a6a6a;
    --clear-gray-500: #8a8a8a;
    --clear-gray-600: #a0a0a0;
    --clear-gray-700: #c0c0c0;
    --clear-gray-800: #e0e0e0;
    --clear-gray-900: #ffffff;
  }
}

[data-theme="dark"] {
  --clear-white: #1a1a1a;
  --clear-gray-50: #2d2d2d;
  --clear-gray-100: #3a3a3a;
  --clear-gray-200: #4a4a4a;
  --clear-gray-300: #5a5a5a;
  --clear-gray-400: #6a6a6a;
  --clear-gray-500: #8a8a8a;
  --clear-gray-600: #a0a0a0;
  --clear-gray-700: #c0c0c0;
  --clear-gray-800: #e0e0e0;
  --clear-gray-900: #ffffff;
}
